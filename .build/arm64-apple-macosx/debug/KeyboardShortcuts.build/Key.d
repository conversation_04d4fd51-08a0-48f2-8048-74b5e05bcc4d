/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Key.swift.o : /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/NSMenuItem++.swift /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/RecorderCocoa.swift /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Name.swift /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Recorder.swift /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/DerivedSources/resource_bundle_accessor.swift /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Utilities.swift /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/ViewModifiers.swift /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/KeyboardShortcuts.swift /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/CarbonKeyboardShortcuts.swift /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Shortcut.swift /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Key.swift /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/XPC.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/ObjectiveC.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/SwiftUI.framework/Modules/SwiftUI.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/simd.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/unistd.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/CoreImage.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/_time.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/sys_time.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/SwiftUICore.framework/Modules/SwiftUICore.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/QuartzCore.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/_StringProcessing.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/OSLog.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/Dispatch.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/_math.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/Spatial.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/_signal.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/Metal.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/System.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/Darwin.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/CoreFoundation.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/Observation.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/DataDetection.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/_stdio.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/_errno.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/os.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/_Builtin_float.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/Swift.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/IOKit.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/AppKit.framework/Modules/AppKit.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/SwiftOnoneSupport.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift/_Concurrency.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/arm64e-apple-macos.swiftinterface /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/XPC.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/ObjectiveC.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/_time.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Dispatch.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/ApplicationServices.framework/Headers/ApplicationServices.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/os.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/AppKit.framework/Headers/AppKit.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks/Security.framework/Headers/Security.apinotes
