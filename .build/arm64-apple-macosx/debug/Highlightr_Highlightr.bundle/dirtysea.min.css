/*!
  Theme: dirtysea
  Author: <PERSON><PERSON><PERSON> (<PERSON><PERSON>) <PERSON><PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#000;background:#e0e0e0}.hljs ::selection,.hljs::selection{background-color:#d0d0d0;color:#000}.hljs-comment{color:#707070}.hljs-tag{color:#202020}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#000}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#840000}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#006565}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#755b00}.hljs-strong{font-weight:700;color:#755b00}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#730073}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#755b00}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#007300}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#000090}.hljs-emphasis{color:#000090;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#755b00}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}