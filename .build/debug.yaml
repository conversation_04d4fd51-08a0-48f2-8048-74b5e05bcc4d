client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "CAtomic-arm64-apple-macosx15.0-debug.module": ["<CAtomic-arm64-apple-macosx15.0-debug.module>"]
  "Highlightr-arm64-apple-macosx15.0-debug.module": ["<Highlightr-arm64-apple-macosx15.0-debug.module>"]
  "KeyboardShortcuts-arm64-apple-macosx15.0-debug.module": ["<KeyboardShortcuts-arm64-apple-macosx15.0-debug.module>"]
  "Markdown-arm64-apple-macosx15.0-debug.module": ["<Markdown-arm64-apple-macosx15.0-debug.module>"]
  "Nexus-arm64-apple-macosx15.0-debug.exe": ["<Nexus-arm64-apple-macosx15.0-debug.exe>"]
  "Nexus-arm64-apple-macosx15.0-debug.module": ["<Nexus-arm64-apple-macosx15.0-debug.module>"]
  "PackageStructure": ["<PackageStructure>"]
  "api_test-arm64-apple-macosx15.0-debug.exe": ["<api_test-arm64-apple-macosx15.0-debug.exe>"]
  "api_test-arm64-apple-macosx15.0-debug.module": ["<api_test-arm64-apple-macosx15.0-debug.module>"]
  "cmark-gfm-arm64-apple-macosx15.0-debug.module": ["<cmark-gfm-arm64-apple-macosx15.0-debug.module>"]
  "cmark-gfm-bin-arm64-apple-macosx15.0-debug.exe": ["<cmark-gfm-bin-arm64-apple-macosx15.0-debug.exe>"]
  "cmark-gfm-bin-arm64-apple-macosx15.0-debug.module": ["<cmark-gfm-bin-arm64-apple-macosx15.0-debug.module>"]
  "cmark-gfm-extensions-arm64-apple-macosx15.0-debug.module": ["<cmark-gfm-extensions-arm64-apple-macosx15.0-debug.module>"]
  "main": ["<Nexus-arm64-apple-macosx15.0-debug.exe>","<Nexus-arm64-apple-macosx15.0-debug.module>"]
  "test": ["<Nexus-arm64-apple-macosx15.0-debug.exe>","<Nexus-arm64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/Volumes/ssd/projects/nexus/Sources/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus":
    is-mutated: true
  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test":
    is-mutated: true
  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin":
    is-mutated: true
commands:
  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/CAtomic.build/CAtomic.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/CAtomic/CAtomic.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/CAtomic.build/CAtomic.c.o"]
    description: "Compiling CAtomic CAtomic.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=CAtomic","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/CAtomic/include","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/CAtomic.build/CAtomic.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/CAtomic/CAtomic.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/CAtomic.build/CAtomic.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/CAtomic.build/CAtomic.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/classes/CodeAttributedString.swift","/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/classes/HTMLUtils.swift","/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/classes/Highlightr.swift","/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/classes/Shims.swift","/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/classes/Theme.swift","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/sources"]
    description: "Write auxiliary file /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/sources"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/1c-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/1c-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/1c-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/1c-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/3024.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/3024.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/3024.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/3024.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/a11y-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/a11y-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/a11y-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/a11y-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/agate.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/agate.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/agate.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/agate.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/an-old-hope.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/an-old-hope.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/an-old-hope.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/an-old-hope.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/androidstudio.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/androidstudio.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/androidstudio.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/androidstudio.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apathy.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/apathy.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apathy.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/apathy.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apprentice.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/apprentice.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apprentice.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/apprentice.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arduino-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/arduino-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arduino-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/arduino-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arta.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/arta.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arta.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/arta.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ascetic.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ascetic.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ascetic.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ascetic.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ashes.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ashes.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ashes.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ashes.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-cave-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-cave-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-cave-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-cave-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-cave.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-cave.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-dune-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-dune-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-dune-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-dune-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-dune.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-dune.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-estuary-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-estuary-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-estuary-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-estuary-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-estuary.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-estuary.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-forest-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-forest-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-forest-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-forest-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-forest.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-forest.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-heath-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-heath-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-heath-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-heath-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-heath.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-heath.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-lakeside-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-lakeside-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-lakeside-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-lakeside-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-lakeside.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-lakeside.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-plateau-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-plateau-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-plateau-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-plateau-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-plateau.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-plateau.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-savanna-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-savanna-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-savanna-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-savanna-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-savanna.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-savanna.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-seaside-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-seaside-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-seaside-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-seaside-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-seaside.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-seaside.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atelier-sulphurpool.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atlas.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atlas.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atlas.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atlas.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark-reasonable.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atom-one-dark-reasonable.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark-reasonable.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atom-one-dark-reasonable.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atom-one-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atom-one-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atom-one-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/atom-one-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bespin.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/bespin.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bespin.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/bespin.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-bathory.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-bathory.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-bathory.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-bathory.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-burzum.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-burzum.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-burzum.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-burzum.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-dark-funeral.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-dark-funeral.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-dark-funeral.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-dark-funeral.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-gorgoroth.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-gorgoroth.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-gorgoroth.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-gorgoroth.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-immortal.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-immortal.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-immortal.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-immortal.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-khold.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-khold.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-khold.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-khold.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-marduk.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-marduk.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-marduk.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-marduk.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-mayhem.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-mayhem.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-mayhem.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-mayhem.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-nile.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-nile.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-nile.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-nile.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-venom.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-venom.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-venom.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal-venom.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/black-metal.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brewer.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/brewer.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brewer.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/brewer.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bright.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/bright.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bright.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/bright.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brogrammer.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/brogrammer.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brogrammer.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/brogrammer.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brown-paper.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/brown-paper.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brown-paper.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/brown-paper.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/brush-trees-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/brush-trees-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/brush-trees.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/brush-trees.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/chalk.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/chalk.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/chalk.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/chalk.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/circus.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/circus.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/circus.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/circus.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/classic-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/classic-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/classic-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/classic-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codepen-embed.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/codepen-embed.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codepen-embed.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/codepen-embed.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codeschool.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/codeschool.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codeschool.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/codeschool.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/color-brewer.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/color-brewer.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/color-brewer.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/color-brewer.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/colors.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/colors.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/colors.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/colors.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupcake.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/cupcake.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupcake.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/cupcake.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupertino.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/cupertino.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupertino.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/cupertino.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-cherry.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/cybertopia-cherry.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-cherry.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/cybertopia-cherry.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-dimmer.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/cybertopia-dimmer.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-dimmer.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/cybertopia-dimmer.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-icecap.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/cybertopia-icecap.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-icecap.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/cybertopia-icecap.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-saturated.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/cybertopia-saturated.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-saturated.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/cybertopia-saturated.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/danqing.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/danqing.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/danqing.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/danqing.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darcula.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/darcula.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darcula.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/darcula.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark-violet.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/dark-violet.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark-violet.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/dark-violet.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darkmoss.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/darkmoss.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darkmoss.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/darkmoss.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darktooth.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/darktooth.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darktooth.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/darktooth.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/decaf.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/decaf.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/decaf.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/decaf.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/default-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/default-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/default-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/default-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/default.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/default.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/devibeans.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/devibeans.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/devibeans.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/devibeans.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dirtysea.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/dirtysea.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dirtysea.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/dirtysea.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/docco.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/docco.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/docco.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/docco.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dracula.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/dracula.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dracula.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/dracula.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/edge-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/edge-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/edge-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/edge-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eighties.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/eighties.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eighties.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/eighties.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/embers.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/embers.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/embers.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/embers.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/equilibrium-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/equilibrium-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/equilibrium-gray-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/equilibrium-gray-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/equilibrium-gray-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/equilibrium-gray-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/equilibrium-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/equilibrium-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/espresso.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/espresso.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/espresso.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/espresso.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva-dim.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/eva-dim.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva-dim.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/eva-dim.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/eva.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/eva.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/far.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/far.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/far.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/far.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/felipec.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/felipec.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/felipec.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/felipec.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/flat.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/flat.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/flat.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/flat.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/foundation.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/foundation.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/foundation.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/foundation.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/framer.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/framer.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/framer.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/framer.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/fruit-soda.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/fruit-soda.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/fruit-soda.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/fruit-soda.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gigavolt.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gigavolt.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gigavolt.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gigavolt.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark-dimmed.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/github-dark-dimmed.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark-dimmed.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/github-dark-dimmed.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/github-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/github-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-gist.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/github-gist.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-gist.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/github-gist.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/github.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/github.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gml.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gml.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gml.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gml.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/google-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/google-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/google-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/google-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/googlecode.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/googlecode.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/googlecode.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/googlecode.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gradient-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gradient-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gradient-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gradient-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/grayscale-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/grayscale-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/grayscale-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/grayscale-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/grayscale.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/grayscale.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/green-screen.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/green-screen.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/green-screen.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/green-screen.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-hard.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-hard.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-hard.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-hard.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-medium.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-medium.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-medium.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-medium.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-pale.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-pale.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-pale.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-pale.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-soft.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-soft.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-soft.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-dark-soft.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-hard.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-light-hard.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-hard.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-light-hard.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-medium.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-light-medium.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-medium.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-light-medium.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-soft.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-light-soft.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-soft.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-light-soft.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/gruvbox-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hardcore.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/hardcore.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hardcore.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/hardcore.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/harmonic16-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/harmonic16-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/harmonic16-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/harmonic16-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/heetch-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/heetch-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/heetch-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/heetch-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/helios.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/helios.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/helios.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/helios.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/highlight.min.js":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/highlighter/highlight.min.js"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/highlight.min.js"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/highlighter/highlight.min.js"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hopscotch.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/hopscotch.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hopscotch.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/hopscotch.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/horizon-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/horizon-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/horizon-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/horizon-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/humanoid-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/humanoid-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/humanoid-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/humanoid-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hybrid.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/hybrid.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hybrid.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/hybrid.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ia-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ia-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ia-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ia-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/icy-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/icy-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/icy-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/icy-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/idea.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/idea.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/idea.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/idea.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/intellij-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/intellij-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/intellij-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/intellij-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ir-black.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ir-black.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ir-black.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ir-black.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/isbl-editor-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/isbl-editor-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/isbl-editor-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/isbl-editor-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isotope.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/isotope.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isotope.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/isotope.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimber.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/kimber.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimber.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/kimber.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/kimbie-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/kimbie-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/kimbie-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/kimbie-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/kimbie.dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/kimbie.dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/kimbie.light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/kimbie.light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lightfair.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/lightfair.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lightfair.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/lightfair.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lioshi.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/lioshi.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lioshi.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/lioshi.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/london-tube.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/london-tube.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/london-tube.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/london-tube.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/macintosh.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/macintosh.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/macintosh.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/macintosh.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/magula.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/magula.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/magula.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/magula.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/marrakesh.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/marrakesh.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/marrakesh.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/marrakesh.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/materia.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/materia.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/materia.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/materia.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-darker.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/material-darker.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-darker.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/material-darker.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-lighter.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/material-lighter.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-lighter.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/material-lighter.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-palenight.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/material-palenight.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-palenight.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/material-palenight.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-vivid.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/material-vivid.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-vivid.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/material-vivid.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/material.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/material.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mellow-purple.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/mellow-purple.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mellow-purple.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/mellow-purple.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mexico-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/mexico-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mexico-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/mexico-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mocha.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/mocha.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mocha.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/mocha.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mono-blue.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/mono-blue.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mono-blue.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/mono-blue.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai-sublime.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/monokai-sublime.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai-sublime.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/monokai-sublime.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/monokai.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/monokai.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nebula.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/nebula.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nebula.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/nebula.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/night-owl.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/night-owl.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/night-owl.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/night-owl.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/nnfx-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/nnfx-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/nnfx-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/nnfx-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nord.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/nord.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nord.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/nord.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nova.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/nova.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nova.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/nova.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/obsidian.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/obsidian.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/obsidian.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/obsidian.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ocean.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ocean.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ocean.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ocean.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/oceanicnext.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/oceanicnext.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/oceanicnext.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/oceanicnext.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/one-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/one-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/one-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/one-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/onedark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/onedark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/onedark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/onedark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/outrun-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/outrun-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/outrun-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/outrun-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/panda-syntax-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/panda-syntax-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/panda-syntax-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/panda-syntax-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/papercolor-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/papercolor-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/papercolor-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/papercolor-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/paraiso-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/paraiso-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/paraiso-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/paraiso-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/paraiso.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/paraiso.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pasque.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/pasque.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pasque.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/pasque.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/phd.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/phd.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/phd.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/phd.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pico.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/pico.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pico.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/pico.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pojoaque.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/pojoaque.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pojoaque.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/pojoaque.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pop.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/pop.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pop.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/pop.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/porple.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/porple.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/porple.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/porple.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/purebasic.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/purebasic.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/purebasic.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/purebasic.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/qtcreator-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/qtcreator-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/qtcreator-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/qtcreator-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/qtcreator_dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/qtcreator_dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/qtcreator_light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/qtcreator_light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qualia.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/qualia.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qualia.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/qualia.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/railscasts.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/railscasts.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/railscasts.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/railscasts.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rainbow.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/rainbow.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rainbow.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/rainbow.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rebecca.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/rebecca.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rebecca.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/rebecca.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-dawn.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ros-pine-dawn.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-dawn.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ros-pine-dawn.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-moon.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ros-pine-moon.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-moon.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ros-pine-moon.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ros-pine.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/ros-pine.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-dawn.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/rose-pine-dawn.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-dawn.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/rose-pine-dawn.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-moon.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/rose-pine-moon.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-moon.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/rose-pine-moon.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/rose-pine.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/rose-pine.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/routeros.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/routeros.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/routeros.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/routeros.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sagelight.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/sagelight.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sagelight.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/sagelight.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sandcastle.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/sandcastle.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sandcastle.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/sandcastle.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/school-book.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/school-book.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/school-book.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/school-book.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/seti-ui.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/seti-ui.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/seti-ui.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/seti-ui.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shades-of-purple.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/shades-of-purple.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shades-of-purple.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/shades-of-purple.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shapeshifter.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/shapeshifter.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shapeshifter.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/shapeshifter.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/silk-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/silk-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/silk-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/silk-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/snazzy.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/snazzy.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/snazzy.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/snazzy.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/solar-flare-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/solar-flare-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/solar-flare.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/solar-flare.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/solarized-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/solarized-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/solarized-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/solarized-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/spacemacs.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/spacemacs.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/spacemacs.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/spacemacs.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/srcery.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/srcery.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/srcery.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/srcery.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/stackoverflow-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/stackoverflow-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/stackoverflow-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/stackoverflow-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summercamp.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/summercamp.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summercamp.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/summercamp.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/summerfruit-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/summerfruit-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/summerfruit-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/summerfruit-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sunburst.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/sunburst.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sunburst.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/sunburst.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/synth-midnight-terminal-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/synth-midnight-terminal-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/synth-midnight-terminal-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/synth-midnight-terminal-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tango.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tango.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tango.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tango.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tender.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tender.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tender.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tender.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tokyo-night-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tokyo-night-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tokyo-night-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tokyo-night-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-blue.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tomorrow-night-blue.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-blue.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tomorrow-night-blue.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-bright.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tomorrow-night-bright.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-bright.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tomorrow-night-bright.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-eighties.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tomorrow-night-eighties.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-eighties.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tomorrow-night-eighties.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tomorrow-night.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tomorrow-night.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tomorrow.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/tomorrow.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/twilight.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/twilight.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/twilight.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/twilight.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/unikitty-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/unikitty-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/unikitty-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/unikitty-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/vs.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/vs.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs2015.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/vs2015.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs2015.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/vs2015.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vulcan.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/vulcan.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vulcan.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/vulcan.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-10-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-10-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-10.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-10.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-95-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-95-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-95.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-95.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-high-contrast-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-high-contrast-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-high-contrast.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-high-contrast.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt-light.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-nt-light.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt-light.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-nt-light.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-nt.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/windows-nt.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/woodland.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/woodland.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/woodland.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/woodland.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dark.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/xcode-dark.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dark.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/xcode-dark.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dusk.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/xcode-dusk.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dusk.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/xcode-dusk.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/xcode.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/xcode.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xt256.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/xt256.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xt256.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/xt256.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/zenburn.min.css":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/zenburn.min.css"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/zenburn.min.css"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/assets/styles/zenburn.min.css"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/CarbonKeyboardShortcuts.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Key.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/KeyboardShortcuts.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/NSMenuItem++.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Name.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Recorder.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/RecorderCocoa.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Shortcut.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Utilities.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/ViewModifiers.swift","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources"]
    description: "Write auxiliary file /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/Info.plist":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Info.plist"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/Info.plist"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Info.plist"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ar.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ar.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ar.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ar.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/cs.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/cs.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/cs.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/cs.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/de.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/de.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/de.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/de.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/en.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/en.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/en.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/en.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/es.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/es.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/es.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/es.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/fr.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/fr.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/fr.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/fr.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/hu.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/hu.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/hu.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/hu.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ja.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ja.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ja.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ja.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ko.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ko.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ko.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ko.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/nl.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/nl.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/nl.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/nl.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/pt-br.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/pt-BR.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/pt-br.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/pt-BR.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ru.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ru.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ru.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/ru.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/sk.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/sk.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/sk.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/sk.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-hans.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/zh-Hans.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-hans.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/zh-Hans.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-tw.lproj/Localizable.strings":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/zh-TW.lproj/Localizable.strings"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-tw.lproj/Localizable.strings"]
    description: "Copying /Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Localization/zh-TW.lproj/Localizable.strings"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/ChildIndexPath.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/DirectiveArgument.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/Document.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/LiteralMarkup.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/Markup.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupChildren.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupData.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/PlainTextConvertibleMarkup.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/RawMarkup.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockDirective.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockQuote.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/CustomBlock.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenDiscussion.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenNote.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenParameter.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenReturns.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/ListItem.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/OrderedList.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/UnorderedList.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Inline Container Blocks/Paragraph.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/CodeBlock.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/HTMLBlock.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/Heading.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/ThematicBreak.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/Table.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableBody.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCell.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCellContainer.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableHead.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableRow.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/Replacement.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/SourceLocation.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Emphasis.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Image.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/InlineAttributes.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Link.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strikethrough.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strong.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/CustomInline.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineCode.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineHTML.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/LineBreak.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SoftBreak.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SymbolLink.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/Text.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Interpretive Nodes/Aside.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Parser/BlockDirectiveParser.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Parser/CommonMarkConverter.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Parser/LazySplitLines.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Parser/ParseOptions.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Parser/RangeAdjuster.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Parser/RangerTracker.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Rewriter/MarkupRewriter.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicBlockContainer.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicInlineContainer.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockContainer.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockMarkup.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineContainer.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineMarkup.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/ListItemContainer.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Utility/AtomicCounter.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Utility/CharacterExtensions.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Utility/CollectionExtensions.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Utility/StringExtensions.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Visitor/MarkupVisitor.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Walker/MarkupWalker.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/HTMLFormatter.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupFormatter.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupTreeDumper.swift"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/sources"]
    description: "Write auxiliary file /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/sources"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus-entitlement.plist":
    tool: write-auxiliary-file
    inputs: ["<entitlement-plist>","<com.apple.security.get-task-allow>"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus-entitlement.plist"]
    description: "Write auxiliary file /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus-entitlement.plist"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Volumes/ssd/projects/nexus/Sources/Nexus/AppDelegate.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/Core/ClipboardService.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/Core/GlobalHotkeyService.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/Core/Persistence.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/Core/SearchService.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/Features/NotesManager.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/UI/ContentView.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/UI/PlainTextEditor.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/main.swift","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/sources"]
    description: "Write auxiliary file /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/sources"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/CAtomic.build/CAtomic.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/CodeAttributedString.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/HTMLUtils.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Highlightr.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Shims.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Theme.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/resource_bundle_accessor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/CarbonKeyboardShortcuts.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Key.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/KeyboardShortcuts.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/NSMenuItem++.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Name.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Recorder.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/RecorderCocoa.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Shortcut.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Utilities.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/ViewModifiers.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/resource_bundle_accessor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Aside.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/AtomicCounter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BasicBlockContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BasicInlineContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockDirective.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockDirectiveParser.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockQuote.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CharacterExtensions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ChildIndexPath.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CodeBlock.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CollectionExtensions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CommonMarkConverter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CustomBlock.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CustomInline.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DirectiveArgument.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Document.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenDiscussion.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenNote.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenParameter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenReturns.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Emphasis.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/HTMLBlock.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/HTMLFormatter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Heading.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Image.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineAttributes.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineCode.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineHTML.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/LazySplitLines.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/LineBreak.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Link.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ListItem.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ListItemContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/LiteralMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Markup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupChildren.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupData.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupFormatter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupRewriter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupTreeDumper.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupVisitor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupWalker.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/OrderedList.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Paragraph.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ParseOptions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/PlainTextConvertibleMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/RangeAdjuster.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/RangerTracker.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/RawMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Replacement.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/SoftBreak.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/SourceLocation.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Strikethrough.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/StringExtensions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Strong.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/SymbolLink.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Table.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableBody.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableCell.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableCellContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableHead.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableRow.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Text.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ThematicBreak.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/UnorderedList.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/AppDelegate.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/ClipboardService.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/ContentView.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/GlobalHotkeyService.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/NotesManager.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/Persistence.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/PlainTextEditor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/SearchService.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/main.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/resource_bundle_accessor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.o"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.product/Objects.LinkFileList"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus_Nexus.bundle/Assets.xcassets":
    tool: copy-tool
    inputs: ["/Volumes/ssd/projects/nexus/Sources/Resources/Assets.xcassets/"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus_Nexus.bundle/Assets.xcassets/"]
    description: "Copying /Volumes/ssd/projects/nexus/Sources/Resources/Assets.xcassets"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test-entitlement.plist":
    tool: write-auxiliary-file
    inputs: ["<entitlement-plist>","<com.apple.security.get-task-allow>"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test-entitlement.plist"]
    description: "Write auxiliary file /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test-entitlement.plist"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/cplusplus.cpp.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/api_test/cplusplus.cpp"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/cplusplus.cpp.o"]
    description: "Compiling api_test cplusplus.cpp"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/api_test/include","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include/module.modulemap","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/cplusplus.cpp.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/api_test/cplusplus.cpp","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/cplusplus.cpp.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/cplusplus.cpp.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/harness.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/api_test/harness.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/harness.c.o"]
    description: "Compiling api_test harness.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=api_test","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/api_test/include","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include/module.modulemap","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/harness.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/api_test/harness.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/harness.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/harness.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/main.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/api_test/main.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/main.c.o"]
    description: "Compiling api_test main.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=api_test","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/api_test/include","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include/module.modulemap","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/main.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/api_test/main.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/main.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/main.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/cplusplus.cpp.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/harness.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/main.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.o"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.product/Objects.LinkFileList"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin-entitlement.plist":
    tool: write-auxiliary-file
    inputs: ["<entitlement-plist>","<com.apple.security.get-task-allow>"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin-entitlement.plist"]
    description: "Write auxiliary file /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin-entitlement.plist"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_bin.build/main.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.o"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin.product/Objects.LinkFileList"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/arena.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.o"]
    description: "Compiling cmark-gfm arena.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/arena.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/blocks.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.o"]
    description: "Compiling cmark-gfm blocks.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/blocks.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/buffer.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.o"]
    description: "Compiling cmark-gfm buffer.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/buffer.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/cmark.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.o"]
    description: "Compiling cmark-gfm cmark.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/cmark.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/cmark_ctype.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.o"]
    description: "Compiling cmark-gfm cmark_ctype.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/cmark_ctype.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/commonmark.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.o"]
    description: "Compiling cmark-gfm commonmark.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/commonmark.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/footnotes.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.o"]
    description: "Compiling cmark-gfm footnotes.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/footnotes.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/houdini_href_e.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.o"]
    description: "Compiling cmark-gfm houdini_href_e.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/houdini_href_e.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/houdini_html_e.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.o"]
    description: "Compiling cmark-gfm houdini_html_e.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/houdini_html_e.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/houdini_html_u.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.o"]
    description: "Compiling cmark-gfm houdini_html_u.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/houdini_html_u.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/html.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.o"]
    description: "Compiling cmark-gfm html.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/html.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/inlines.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.o"]
    description: "Compiling cmark-gfm inlines.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/inlines.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/iterator.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.o"]
    description: "Compiling cmark-gfm iterator.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/iterator.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/latex.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.o"]
    description: "Compiling cmark-gfm latex.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/latex.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/linked_list.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.o"]
    description: "Compiling cmark-gfm linked_list.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/linked_list.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/man.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.o"]
    description: "Compiling cmark-gfm man.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/man.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/map.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.o"]
    description: "Compiling cmark-gfm map.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/map.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/node.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.o"]
    description: "Compiling cmark-gfm node.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/node.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/plaintext.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.o"]
    description: "Compiling cmark-gfm plaintext.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/plaintext.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/plugin.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.o"]
    description: "Compiling cmark-gfm plugin.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/plugin.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/references.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.o"]
    description: "Compiling cmark-gfm references.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/references.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/registry.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.o"]
    description: "Compiling cmark-gfm registry.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/registry.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/render.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.o"]
    description: "Compiling cmark-gfm render.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/render.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/scanners.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.o"]
    description: "Compiling cmark-gfm scanners.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/scanners.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/syntax_extension.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.o"]
    description: "Compiling cmark-gfm syntax_extension.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/syntax_extension.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/utf8.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.o"]
    description: "Compiling cmark-gfm utf8.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/utf8.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/xml.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.o"]
    description: "Compiling cmark-gfm xml.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/xml.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_bin.build/main.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/bin/main.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_bin.build/main.c.o"]
    description: "Compiling cmark-gfm-bin main.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm_bin","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/bin/include","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include/module.modulemap","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_bin.build/main.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/bin/main.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_bin.build/main.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_bin.build/main.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/autolink.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.o"]
    description: "Compiling cmark-gfm-extensions autolink.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm_extensions","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/autolink.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/core-extensions.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.o"]
    description: "Compiling cmark-gfm-extensions core-extensions.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm_extensions","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/core-extensions.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/ext_scanners.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.o"]
    description: "Compiling cmark-gfm-extensions ext_scanners.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm_extensions","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/ext_scanners.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/strikethrough.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.o"]
    description: "Compiling cmark-gfm-extensions strikethrough.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm_extensions","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/strikethrough.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/table.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.o"]
    description: "Compiling cmark-gfm-extensions table.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm_extensions","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/table.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/tagfilter.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.o"]
    description: "Compiling cmark-gfm-extensions tagfilter.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm_extensions","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/tagfilter.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.o":
    tool: clang
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/tasklist.c"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.o"]
    description: "Compiling cmark-gfm-extensions tasklist.c"
    args: ["/Library/Developer/CommandLineTools/usr/bin/clang","-fobjc-arc","-target","arm64-apple-macosx10.13","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-fmodules","-fmodule-name=cmark_gfm_extensions","-fmodules-cache-path=/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-I","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-DCMARK_THREADING","-isysroot","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-fPIC","-g","-w","-MD","-MT","dependencies","-MF","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.d","-c","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/tasklist.c","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.o"]
    deps: "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.d"

  "/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/swift-version--1AB21518FC5DEDBE.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Library/Developer/CommandLineTools/usr/bin/swiftc"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/swift-version--1AB21518FC5DEDBE.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/swift-version--1AB21518FC5DEDBE.txt"

  "<CAtomic-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/CAtomic.build/CAtomic.c.o"]
    outputs: ["<CAtomic-arm64-apple-macosx15.0-debug.module>"]

  "<Highlightr-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/CodeAttributedString.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/HTMLUtils.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Highlightr.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Shims.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Theme.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/resource_bundle_accessor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Highlightr.swiftmodule"]
    outputs: ["<Highlightr-arm64-apple-macosx15.0-debug.module>"]

  "<KeyboardShortcuts-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/CarbonKeyboardShortcuts.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Key.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/KeyboardShortcuts.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/NSMenuItem++.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Name.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Recorder.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/RecorderCocoa.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Shortcut.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Utilities.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/ViewModifiers.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/resource_bundle_accessor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/KeyboardShortcuts.swiftmodule"]
    outputs: ["<KeyboardShortcuts-arm64-apple-macosx15.0-debug.module>"]

  "<Markdown-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ChildIndexPath.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DirectiveArgument.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Document.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/LiteralMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Markup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupChildren.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupData.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/PlainTextConvertibleMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/RawMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockDirective.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockQuote.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CustomBlock.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenDiscussion.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenNote.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenParameter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenReturns.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ListItem.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/OrderedList.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/UnorderedList.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Paragraph.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CodeBlock.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/HTMLBlock.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Heading.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ThematicBreak.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Table.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableBody.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableCell.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableCellContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableHead.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableRow.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Replacement.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/SourceLocation.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Emphasis.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Image.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineAttributes.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Link.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Strikethrough.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Strong.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CustomInline.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineCode.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineHTML.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/LineBreak.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/SoftBreak.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/SymbolLink.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Text.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Aside.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockDirectiveParser.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CommonMarkConverter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/LazySplitLines.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ParseOptions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/RangeAdjuster.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/RangerTracker.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupRewriter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BasicBlockContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BasicInlineContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ListItemContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/AtomicCounter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CharacterExtensions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CollectionExtensions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/StringExtensions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupVisitor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupWalker.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/HTMLFormatter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupFormatter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupTreeDumper.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Markdown.swiftmodule"]
    outputs: ["<Markdown-arm64-apple-macosx15.0-debug.module>"]

  "<Nexus-arm64-apple-macosx15.0-debug.exe>":
    tool: phony
    inputs: ["<Nexus-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    outputs: ["<Nexus-arm64-apple-macosx15.0-debug.exe>"]

  "<Nexus-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/AppDelegate.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/ClipboardService.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/GlobalHotkeyService.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/Persistence.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/SearchService.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/NotesManager.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/ContentView.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/PlainTextEditor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/main.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/resource_bundle_accessor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Nexus.swiftmodule"]
    outputs: ["<Nexus-arm64-apple-macosx15.0-debug.module>"]

  "<api_test-arm64-apple-macosx15.0-debug.exe>":
    tool: phony
    inputs: ["<api_test-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    outputs: ["<api_test-arm64-apple-macosx15.0-debug.exe>"]

  "<api_test-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/cplusplus.cpp.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/harness.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/main.c.o"]
    outputs: ["<api_test-arm64-apple-macosx15.0-debug.module>"]

  "<cmark-gfm-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.o"]
    outputs: ["<cmark-gfm-arm64-apple-macosx15.0-debug.module>"]

  "<cmark-gfm-bin-arm64-apple-macosx15.0-debug.exe>":
    tool: phony
    inputs: ["<cmark-gfm-bin-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    outputs: ["<cmark-gfm-bin-arm64-apple-macosx15.0-debug.exe>"]

  "<cmark-gfm-bin-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_bin.build/main.c.o"]
    outputs: ["<cmark-gfm-bin-arm64-apple-macosx15.0-debug.module>"]

  "<cmark-gfm-extensions-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.o"]
    outputs: ["<cmark-gfm-extensions-arm64-apple-macosx15.0-debug.module>"]

  "C.Highlightr-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/classes/CodeAttributedString.swift","/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/classes/HTMLUtils.swift","/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/classes/Highlightr.swift","/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/classes/Shims.swift","/Volumes/ssd/projects/nexus/.build/checkouts/Highlightr/src/classes/Theme.swift","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/DerivedSources/resource_bundle_accessor.swift","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/swift-version--1AB21518FC5DEDBE.txt","<Highlightr-arm64-apple-macosx15.0-debug.module-resources>","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/sources"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/CodeAttributedString.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/HTMLUtils.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Highlightr.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Shims.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Theme.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/resource_bundle_accessor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Highlightr.swiftmodule"]
    description: "Compiling Swift Module 'Highlightr' (6 sources)"
    args: ["/Library/Developer/CommandLineTools/usr/bin/swiftc","-module-name","Highlightr","-emit-dependencies","-emit-module","-emit-module-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Highlightr.swiftmodule","-output-file-map","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/sources","-I","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.13","-enable-batch-mode","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Highlightr-Swift.h","-color-diagnostics","-swift-version","5","-F","/Library/Developer/CommandLineTools/Library/Developer/Frameworks","-sdk","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-g","-Xcc","-isysroot","-Xcc","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.KeyboardShortcuts-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/CarbonKeyboardShortcuts.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Key.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/KeyboardShortcuts.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/NSMenuItem++.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Name.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Recorder.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/RecorderCocoa.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Shortcut.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/Utilities.swift","/Volumes/ssd/projects/nexus/.build/checkouts/KeyboardShortcuts/Sources/KeyboardShortcuts/ViewModifiers.swift","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/DerivedSources/resource_bundle_accessor.swift","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/swift-version--1AB21518FC5DEDBE.txt","<KeyboardShortcuts-arm64-apple-macosx15.0-debug.module-resources>","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/CarbonKeyboardShortcuts.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Key.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/KeyboardShortcuts.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/NSMenuItem++.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Name.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Recorder.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/RecorderCocoa.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Shortcut.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Utilities.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/ViewModifiers.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/resource_bundle_accessor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/KeyboardShortcuts.swiftmodule"]
    description: "Compiling Swift Module 'KeyboardShortcuts' (11 sources)"
    args: ["/Library/Developer/CommandLineTools/usr/bin/swiftc","-module-name","KeyboardShortcuts","-emit-dependencies","-emit-module","-emit-module-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/KeyboardShortcuts.swiftmodule","-output-file-map","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/sources","-I","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.15","-enable-batch-mode","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/KeyboardShortcuts-Swift.h","-color-diagnostics","-swift-version","5","-F","/Library/Developer/CommandLineTools/Library/Developer/Frameworks","-sdk","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-g","-Xcc","-isysroot","-Xcc","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings","-package-name","keyboardshortcuts"]

  "C.Markdown-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/ChildIndexPath.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/DirectiveArgument.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/Document.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/LiteralMarkup.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/Markup.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupChildren.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/MarkupData.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/PlainTextConvertibleMarkup.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Base/RawMarkup.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockDirective.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/BlockQuote.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/CustomBlock.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenDiscussion.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenNote.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenParameter.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/Doxygen Commands/DoxygenReturns.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/ListItem.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/OrderedList.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Block Container Blocks/UnorderedList.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Inline Container Blocks/Paragraph.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/CodeBlock.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/HTMLBlock.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/Heading.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Leaf Blocks/ThematicBreak.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/Table.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableBody.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCell.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableCellContainer.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableHead.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Block Nodes/Tables/TableRow.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/Replacement.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Infrastructure/SourceLocation.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Emphasis.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Image.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/InlineAttributes.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Link.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strikethrough.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Containers/Strong.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/CustomInline.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineCode.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/InlineHTML.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/LineBreak.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SoftBreak.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/SymbolLink.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Inline Nodes/Inline Leaves/Text.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Interpretive Nodes/Aside.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Parser/BlockDirectiveParser.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Parser/CommonMarkConverter.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Parser/LazySplitLines.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Parser/ParseOptions.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Parser/RangeAdjuster.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Parser/RangerTracker.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Rewriter/MarkupRewriter.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicBlockContainer.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BasicInlineContainer.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockContainer.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/BlockMarkup.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineContainer.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/InlineMarkup.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Structural Restrictions/ListItemContainer.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Utility/AtomicCounter.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Utility/CharacterExtensions.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Utility/CollectionExtensions.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Utility/StringExtensions.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Visitor/MarkupVisitor.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Walker/MarkupWalker.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/HTMLFormatter.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupFormatter.swift","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/Markdown/Walker/Walkers/MarkupTreeDumper.swift","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/swift-version--1AB21518FC5DEDBE.txt","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/CAtomic.build/CAtomic.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/sources"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ChildIndexPath.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DirectiveArgument.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Document.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/LiteralMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Markup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupChildren.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupData.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/PlainTextConvertibleMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/RawMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockDirective.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockQuote.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CustomBlock.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenDiscussion.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenNote.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenParameter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenReturns.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ListItem.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/OrderedList.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/UnorderedList.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Paragraph.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CodeBlock.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/HTMLBlock.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Heading.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ThematicBreak.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Table.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableBody.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableCell.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableCellContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableHead.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableRow.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Replacement.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/SourceLocation.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Emphasis.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Image.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineAttributes.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Link.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Strikethrough.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Strong.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CustomInline.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineCode.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineHTML.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/LineBreak.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/SoftBreak.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/SymbolLink.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Text.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Aside.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockDirectiveParser.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CommonMarkConverter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/LazySplitLines.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ParseOptions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/RangeAdjuster.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/RangerTracker.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupRewriter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BasicBlockContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BasicInlineContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ListItemContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/AtomicCounter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CharacterExtensions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CollectionExtensions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/StringExtensions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupVisitor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupWalker.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/HTMLFormatter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupFormatter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupTreeDumper.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Markdown.swiftmodule"]
    description: "Compiling Swift Module 'Markdown' (69 sources)"
    args: ["/Library/Developer/CommandLineTools/usr/bin/swiftc","-module-name","Markdown","-emit-dependencies","-emit-module","-emit-module-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Markdown.swiftmodule","-output-file-map","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/sources","-I","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx10.13","-enable-batch-mode","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/CAtomic/include","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-module-cache-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Markdown-Swift.h","-color-diagnostics","-swift-version","5","-F","/Library/Developer/CommandLineTools/Library/Developer/Frameworks","-sdk","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-g","-Xcc","-isysroot","-Xcc","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-Xcc","-fPIC","-Xcc","-g","-suppress-warnings"]

  "C.Nexus-arm64-apple-macosx15.0-debug.exe":
    tool: shell
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/CAtomic.build/CAtomic.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/CodeAttributedString.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/HTMLUtils.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Highlightr.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Shims.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/Theme.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr.build/resource_bundle_accessor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/CarbonKeyboardShortcuts.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Key.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/KeyboardShortcuts.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/NSMenuItem++.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Name.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Recorder.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/RecorderCocoa.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Shortcut.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/Utilities.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/ViewModifiers.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts.build/resource_bundle_accessor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Aside.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/AtomicCounter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BasicBlockContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BasicInlineContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockDirective.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockDirectiveParser.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/BlockQuote.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CharacterExtensions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ChildIndexPath.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CodeBlock.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CollectionExtensions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CommonMarkConverter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CustomBlock.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/CustomInline.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DirectiveArgument.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Document.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenDiscussion.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenNote.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenParameter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/DoxygenReturns.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Emphasis.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/HTMLBlock.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/HTMLFormatter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Heading.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Image.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineAttributes.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineCode.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineHTML.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/InlineMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/LazySplitLines.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/LineBreak.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Link.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ListItem.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ListItemContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/LiteralMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Markup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupChildren.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupData.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupFormatter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupRewriter.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupTreeDumper.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupVisitor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/MarkupWalker.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/OrderedList.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Paragraph.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ParseOptions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/PlainTextConvertibleMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/RangeAdjuster.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/RangerTracker.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/RawMarkup.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Replacement.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/SoftBreak.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/SourceLocation.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Strikethrough.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/StringExtensions.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Strong.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/SymbolLink.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Table.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableBody.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableCell.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableCellContainer.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableHead.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/TableRow.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/Text.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/ThematicBreak.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Markdown.build/UnorderedList.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/AppDelegate.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/ClipboardService.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/ContentView.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/GlobalHotkeyService.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/NotesManager.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/Persistence.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/PlainTextEditor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/SearchService.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/main.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/resource_bundle_accessor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.product/Objects.LinkFileList"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus"]
    description: "Linking ./.build/arm64-apple-macosx/debug/Nexus"
    args: ["/Library/Developer/CommandLineTools/usr/bin/swiftc","-L","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus","-module-name","Nexus","-Xlinker","-no_warn_duplicate_libraries","-emit-executable","-Xlinker","-alias","-Xlinker","_Nexus_main","-Xlinker","_main","-Xlinker","-rpath","-Xlinker","@loader_path","@/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.product/Objects.LinkFileList","-target","arm64-apple-macosx13.0","-Xlinker","-add_ast_path","-Xlinker","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Highlightr.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/KeyboardShortcuts.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Markdown.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Nexus.swiftmodule","-F","/Library/Developer/CommandLineTools/Library/Developer/Frameworks","-sdk","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-g","-Xlinker","-rpath","-Xlinker","/Library/Developer/CommandLineTools/Library/Developer/Frameworks"]

  "C.Nexus-arm64-apple-macosx15.0-debug.exe-entitlements":
    tool: shell
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus-entitlement.plist"]
    outputs: ["<Nexus-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    description: "Applying debug entitlements to ./.build/arm64-apple-macosx/debug/Nexus"
    args: ["codesign","--force","--sign","-","--entitlements","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus-entitlement.plist","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus"]

  "C.Nexus-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Volumes/ssd/projects/nexus/Sources/Nexus/AppDelegate.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/Core/ClipboardService.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/Core/GlobalHotkeyService.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/Core/Persistence.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/Core/SearchService.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/Features/NotesManager.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/UI/ContentView.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/UI/PlainTextEditor.swift","/Volumes/ssd/projects/nexus/Sources/Nexus/main.swift","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/DerivedSources/resource_bundle_accessor.swift","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/swift-version--1AB21518FC5DEDBE.txt","<Nexus-arm64-apple-macosx15.0-debug.module-resources>","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Markdown.swiftmodule","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Highlightr.swiftmodule","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/KeyboardShortcuts.swiftmodule","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/sources"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/AppDelegate.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/ClipboardService.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/GlobalHotkeyService.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/Persistence.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/SearchService.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/NotesManager.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/ContentView.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/PlainTextEditor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/main.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/resource_bundle_accessor.swift.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Nexus.swiftmodule"]
    description: "Compiling Swift Module 'Nexus' (10 sources)"
    args: ["/Library/Developer/CommandLineTools/usr/bin/swiftc","-module-name","Nexus","-emit-dependencies","-emit-module","-emit-module-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules/Nexus.swiftmodule","-output-file-map","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/output-file-map.json","-incremental","-c","@/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus.build/sources","-I","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx13.0","-enable-batch-mode","-index-store-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/extensions/include","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/nexus/.build/checkouts/swift-cmark/src/include","-Xcc","-fmodule-map-file=/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/CAtomic/include/module.modulemap","-Xcc","-I","-Xcc","/Volumes/ssd/projects/nexus/.build/checkouts/swift-markdown/Sources/CAtomic/include","-module-cache-path","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-Xfrontend","-entry-point-function-name","-Xfrontend","Nexus_main","-color-diagnostics","-swift-version","5","-F","/Library/Developer/CommandLineTools/Library/Developer/Frameworks","-sdk","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-g","-Xcc","-isysroot","-Xcc","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-Xcc","-fPIC","-Xcc","-g","-package-name","nexus"]

  "C.api_test-arm64-apple-macosx15.0-debug.exe":
    tool: shell
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/cplusplus.cpp.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/harness.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.build/main.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.product/Objects.LinkFileList"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test"]
    description: "Linking ./.build/arm64-apple-macosx/debug/api_test"
    args: ["/Library/Developer/CommandLineTools/usr/bin/swiftc","-lc++","-L","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test","-module-name","api_test","-Xlinker","-no_warn_duplicate_libraries","-emit-executable","-Xlinker","-rpath","-Xlinker","@loader_path","@/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test.product/Objects.LinkFileList","-runtime-compatibility-version","none","-target","arm64-apple-macosx10.13","-F","/Library/Developer/CommandLineTools/Library/Developer/Frameworks","-sdk","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-g","-Xlinker","-rpath","-Xlinker","/Library/Developer/CommandLineTools/Library/Developer/Frameworks"]

  "C.api_test-arm64-apple-macosx15.0-debug.exe-entitlements":
    tool: shell
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test-entitlement.plist"]
    outputs: ["<api_test-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    description: "Applying debug entitlements to ./.build/arm64-apple-macosx/debug/api_test"
    args: ["codesign","--force","--sign","-","--entitlements","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test-entitlement.plist","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/api_test"]

  "C.cmark-gfm-bin-arm64-apple-macosx15.0-debug.exe":
    tool: shell
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/arena.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/blocks.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/buffer.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/cmark_ctype.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/commonmark.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/footnotes.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_href_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_e.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/houdini_html_u.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/html.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/inlines.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/iterator.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/latex.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/linked_list.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/man.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/map.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/node.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plaintext.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/plugin.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/references.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/registry.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/render.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/syntax_extension.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/utf8.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm.build/xml.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_bin.build/main.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/autolink.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/core-extensions.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/ext_scanners.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/strikethrough.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/table.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tagfilter.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark_gfm_extensions.build/tasklist.c.o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin.product/Objects.LinkFileList"]
    outputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin"]
    description: "Linking ./.build/arm64-apple-macosx/debug/cmark-gfm-bin"
    args: ["/Library/Developer/CommandLineTools/usr/bin/swiftc","-L","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug","-o","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin","-module-name","cmark_gfm_bin","-Xlinker","-no_warn_duplicate_libraries","-emit-executable","-Xlinker","-rpath","-Xlinker","@loader_path","@/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin.product/Objects.LinkFileList","-runtime-compatibility-version","none","-target","arm64-apple-macosx10.13","-F","/Library/Developer/CommandLineTools/Library/Developer/Frameworks","-sdk","/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk","-g","-Xlinker","-rpath","-Xlinker","/Library/Developer/CommandLineTools/Library/Developer/Frameworks"]

  "C.cmark-gfm-bin-arm64-apple-macosx15.0-debug.exe-entitlements":
    tool: shell
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin-entitlement.plist"]
    outputs: ["<cmark-gfm-bin-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    description: "Applying debug entitlements to ./.build/arm64-apple-macosx/debug/cmark-gfm-bin"
    args: ["codesign","--force","--sign","-","--entitlements","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin-entitlement.plist","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/cmark-gfm-bin"]

  "Highlightr-arm64-apple-macosx15.0-debug.module-resources":
    tool: phony
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/highlight.min.js","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/1c-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/3024.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/a11y-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/agate.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/an-old-hope.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/androidstudio.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apathy.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/apprentice.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arduino-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/arta.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ascetic.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ashes.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-cave.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-dune.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-estuary.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-forest.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-heath.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-lakeside.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-plateau.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-savanna.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-seaside.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atelier-sulphurpool.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atlas.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark-reasonable.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/atom-one-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bespin.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-bathory.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-burzum.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-dark-funeral.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-gorgoroth.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-immortal.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-khold.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-marduk.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-mayhem.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-nile.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal-venom.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/black-metal.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brewer.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/bright.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brogrammer.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brown-paper.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/brush-trees.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/chalk.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/circus.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/classic-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codepen-embed.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/codeschool.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/color-brewer.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/colors.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupcake.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cupertino.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-cherry.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-dimmer.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-icecap.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/cybertopia-saturated.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/danqing.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darcula.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark-violet.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darkmoss.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/darktooth.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/decaf.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/default.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/devibeans.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dirtysea.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/docco.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/dracula.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/edge-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eighties.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/embers.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-gray-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/equilibrium-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/espresso.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva-dim.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/eva.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/far.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/felipec.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/flat.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/foundation.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/framer.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/fruit-soda.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gigavolt.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark-dimmed.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github-gist.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/github.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gml.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/google-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/googlecode.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gradient-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/grayscale.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/green-screen.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-hard.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-medium.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-pale.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark-soft.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-hard.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-medium.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light-soft.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/gruvbox-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hardcore.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/harmonic16-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/heetch-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/helios.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hopscotch.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/horizon-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/humanoid-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/hybrid.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ia-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/icy-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/idea.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/intellij-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ir-black.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isbl-editor-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/isotope.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimber.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/kimbie.light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lightfair.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/lioshi.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/london-tube.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/macintosh.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/magula.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/marrakesh.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/materia.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-darker.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-lighter.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-palenight.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material-vivid.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/material.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mellow-purple.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mexico-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mocha.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/mono-blue.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai-sublime.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/monokai.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nebula.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/night-owl.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nnfx-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nord.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/nova.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/obsidian.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ocean.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/oceanicnext.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/one-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/onedark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/outrun-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/panda-syntax-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/papercolor-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/paraiso.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pasque.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/phd.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pico.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pojoaque.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/pop.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/porple.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/purebasic.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qtcreator_light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/qualia.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/railscasts.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rainbow.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rebecca.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-dawn.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine-moon.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/ros-pine.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-dawn.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine-moon.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/rose-pine.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/routeros.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sagelight.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sandcastle.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/school-book.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/seti-ui.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shades-of-purple.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/shapeshifter.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/silk-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/snazzy.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solar-flare.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/solarized-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/spacemacs.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/srcery.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/stackoverflow-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summercamp.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/summerfruit-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/sunburst.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/synth-midnight-terminal-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tango.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tender.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tokyo-night-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-blue.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-bright.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night-eighties.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow-night.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/tomorrow.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/twilight.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/unikitty-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vs2015.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/vulcan.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-10.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-95.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-high-contrast.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt-light.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/windows-nt.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/woodland.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dark.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode-dusk.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xcode.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/xt256.min.css","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Highlightr_Highlightr.bundle/zenburn.min.css"]
    outputs: ["<Highlightr-arm64-apple-macosx15.0-debug.module-resources>"]

  "KeyboardShortcuts-arm64-apple-macosx15.0-debug.module-resources":
    tool: phony
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ar.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/cs.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/de.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/en.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/es.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/fr.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/hu.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ja.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ko.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/nl.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/pt-br.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/ru.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/sk.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-hans.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/zh-tw.lproj/Localizable.strings","/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/KeyboardShortcuts_KeyboardShortcuts.bundle/Info.plist"]
    outputs: ["<KeyboardShortcuts-arm64-apple-macosx15.0-debug.module-resources>"]

  "Nexus-arm64-apple-macosx15.0-debug.module-resources":
    tool: phony
    inputs: ["/Volumes/ssd/projects/nexus/.build/arm64-apple-macosx/debug/Nexus_Nexus.bundle/Assets.xcassets/"]
    outputs: ["<Nexus-arm64-apple-macosx15.0-debug.module-resources>"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Volumes/ssd/projects/nexus/Sources/","/Volumes/ssd/projects/nexus/Package.swift","/Volumes/ssd/projects/nexus/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

