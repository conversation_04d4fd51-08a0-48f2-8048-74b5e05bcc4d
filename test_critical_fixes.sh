#!/bin/bash

# Comprehensive test for critical Nexus fixes
echo "🧪 Testing Critical Nexus Fixes"
echo "================================"

# Check if Nexus is running
if ! pgrep -f "Nexus" > /dev/null; then
    echo "❌ Nexus app is not running. Please start it first."
    exit 1
fi

echo "✅ Nexus app is running"
echo ""

echo "🔧 FIXES APPLIED:"
echo "1. ✅ Removed forced .vibrantLight appearance from popover"
echo "2. ✅ Switched to system-appropriate colors (NSColor.labelColor, NSColor.textBackgroundColor)"
echo "3. ✅ Enhanced text input configuration (isEditable, isSelectable, allowsUndo)"
echo "4. ✅ Removed problematic timer-based color overrides"
echo "5. ✅ Set textView.appearance = nil to respect system appearance"
echo ""

echo "📋 TESTING INSTRUCTIONS:"
echo "========================"
echo ""
echo "🎯 Test 1: Duplicate UI Issue"
echo "   - Click the Nexus menu bar icon"
echo "   - Verify only ONE interface appears (not two)"
echo "   - Try right-clicking the icon and selecting 'Open in Window'"
echo "   - Verify the window mode works without duplicate interfaces"
echo ""

echo "🎯 Test 2: Text Input Functionality"
echo "   - Create a new note by clicking the '+' button"
echo "   - Try typing text in the editor"
echo "   - Verify that:"
echo "     ✓ You CAN type (keyboard input works)"
echo "     ✓ Text IS VISIBLE as you type"
echo "     ✓ Text color contrasts properly with background"
echo "     ✓ You can select text with mouse"
echo ""

echo "🎯 Test 3: Appearance Compatibility"
echo "   - Test in both light and dark mode"
echo "   - Change system appearance: System Preferences > General > Appearance"
echo "   - Verify text remains visible in both modes"
echo ""

echo "🔍 Monitoring console for debug messages..."
echo "Press Ctrl+C to stop monitoring"
echo ""

# Monitor for debug messages
timeout 60 log stream --predicate 'process == "Nexus"' --style compact | grep -E "(NEXUS DEBUG|📝|🎨|Creating NSTextView|Text colors set)" | while read line; do
    echo "🔍 $line"
done

echo ""
echo "🏁 Test monitoring complete."
echo ""
echo "💡 If issues persist:"
echo "   - Check that text appears when typing"
echo "   - Verify only one UI interface is visible"
echo "   - Test both popover and window modes"
echo "   - Report any remaining problems"
