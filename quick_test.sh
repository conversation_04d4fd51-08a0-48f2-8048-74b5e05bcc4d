#!/bin/bash

echo "🧪 Quick Test of Nexus Fixes"
echo "============================"

# Check if Nexus is running
if ! pgrep -f "Nexus" > /dev/null; then
    echo "❌ Nexus app is not running. Please start it first."
    exit 1
fi

echo "✅ Nexus app is running"
echo ""
echo "📋 MANUAL TEST CHECKLIST:"
echo "========================"
echo ""
echo "1. 🎯 DUPLICATE UI TEST:"
echo "   □ Click the Nexus menu bar icon"
echo "   □ Verify ONLY ONE interface appears"
echo "   □ Interface should have consistent appearance"
echo ""
echo "2. 🎯 TEXT INPUT TEST:"
echo "   □ Click 'Create New Note' or '+' button"
echo "   □ Try typing: 'Hello World! This is a test.'"
echo "   □ Verify text appears as you type"
echo "   □ Verify text is clearly visible (not invisible)"
echo "   □ Try selecting text with mouse"
echo ""
echo "3. 🎯 APPEARANCE TEST:"
echo "   □ Text should be visible in current system theme"
echo "   □ Background should contrast properly with text"
echo ""
echo "🔍 Checking for debug messages in console..."

# Check for recent debug messages
echo "Recent Nexus debug messages:"
log show --predicate 'process == "Nexus"' --last 1m --style compact | grep -E "(NEXUS DEBUG|📝|🎨)" | tail -10

echo ""
echo "✅ Test setup complete. Please perform the manual tests above."
echo "💡 If text input works and is visible, the critical fixes are successful!"
