#!/bin/bash

# Test script to verify VS Code development setup
# This script tests the basic functionality without requiring full Xcode

set -e

echo "🧪 Testing Nexus VS Code Development Setup"
echo "=========================================="

# Test 1: Check if Swift is available
echo "1. Testing Swift availability..."
if command -v swift >/dev/null 2>&1; then
    echo "✅ Swift found: $(swift --version | head -1)"
else
    echo "❌ Swift not found"
    exit 1
fi

# Test 2: Check Package.swift syntax
echo "2. Testing Package.swift syntax..."
if swift package dump-package >/dev/null 2>&1; then
    echo "✅ Package.swift is valid"
else
    echo "❌ Package.swift has syntax errors"
    exit 1
fi

# Test 3: Check if dependencies can be resolved
echo "3. Testing dependency resolution..."
if swift package resolve; then
    echo "✅ Dependencies resolved successfully"
else
    echo "❌ Dependency resolution failed"
    exit 1
fi

# Test 4: Check VS Code configuration files
echo "4. Testing VS Code configuration..."
if [ -f ".vscode/tasks.json" ] && [ -f ".vscode/launch.json" ] && [ -f ".vscode/settings.json" ]; then
    echo "✅ VS Code configuration files present"
else
    echo "❌ Missing VS Code configuration files"
    exit 1
fi

# Test 5: Check source structure
echo "5. Testing source structure..."
if [ -d "Sources/Nexus" ] && [ -f "Sources/Nexus/main.swift" ]; then
    echo "✅ SPM source structure is correct"
else
    echo "❌ SPM source structure is missing"
    exit 1
fi

# Test 6: Check build script
echo "6. Testing build script..."
if [ -x "build.sh" ]; then
    echo "✅ Build script is executable"
    ./build.sh help >/dev/null 2>&1
    echo "✅ Build script runs correctly"
else
    echo "❌ Build script is not executable or missing"
    exit 1
fi

echo ""
echo "🎉 All tests passed! VS Code development setup is ready."
echo ""
echo "Next steps:"
echo "1. Open VS Code: code ."
echo "2. Install Swift extension if not already installed"
echo "3. Try building: Cmd+Shift+B or ./build.sh build-spm"
echo "4. For Xcode builds: ./build.sh setup (requires sudo)"
echo ""
echo "Note: SPM builds may show warnings due to SwiftUI Preview macros in dependencies."
echo "This is expected and doesn't affect core functionality."
