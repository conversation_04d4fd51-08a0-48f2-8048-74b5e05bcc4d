import SwiftUI
import AppKit

// DIAGNOSTIC: Standalone test app to isolate text view issues
@main
struct TextDiagnosticApp: App {
    var body: some Scene {
        WindowGroup {
            TestTextView()
                .frame(width: 600, height: 500)
        }
        .windowResizability(.contentSize)
    }
}

struct TestTextView: View {
    @State private var testText = "Type here to test visibility..."
    @State private var diagnosticInfo = "Starting diagnostic..."
    
    var body: some View {
        VStack(spacing: 15) {
            Text("🔍 Text Editor Diagnostic Tool")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Diagnostic Info: \(diagnosticInfo)")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Text("Bound Text Value: '\(testText)'")
                .font(.caption)
                .foregroundColor(.blue)
                .padding(.horizontal)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(4)
            
            HStack(spacing: 15) {
                VStack(alignment: .leading) {
                    Text("🧪 Minimal NSTextView")
                        .font(.headline)
                        .foregroundColor(.red)
                    
                    MinimalTextEditor(text: $testText, onDiagnostic: { info in
                        diagnosticInfo = info
                    })
                    .frame(height: 150)
                    .border(Color.red, width: 2)
                }
                
                VStack(alignment: .leading) {
                    Text("✅ SwiftUI TextEditor")
                        .font(.headline)
                        .foregroundColor(.blue)
                    
                    TextEditor(text: $testText)
                        .frame(height: 150)
                        .border(Color.blue, width: 2)
                }
            }
            
            HStack {
                Button("Clear Text") {
                    testText = ""
                    diagnosticInfo = "Text cleared"
                }
                
                Button("Set Test Text") {
                    testText = "Test text set at \(Date().formatted(.dateTime.hour().minute().second()))"
                    diagnosticInfo = "Test text set programmatically"
                }
                
                Button("Focus NSTextView") {
                    // This will be implemented in the coordinator
                    diagnosticInfo = "Attempting to focus NSTextView..."
                }
            }
            
            Text("Instructions: Type in both editors. The red one should behave identically to the blue one.")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
    }
}

struct MinimalTextEditor: NSViewRepresentable {
    @Binding var text: String
    let onDiagnostic: (String) -> Void
    
    func makeNSView(context: Context) -> NSScrollView {
        let scrollView = NSScrollView()
        let textView = NSTextView()
        
        onDiagnostic("Creating NSTextView...")
        
        // CRITICAL: Absolute minimal setup
        textView.isEditable = true
        textView.isSelectable = true
        textView.isRichText = false
        
        // CRITICAL: Explicit colors that should always be visible
        textView.textColor = NSColor.black
        textView.backgroundColor = NSColor.white
        textView.insertionPointColor = NSColor.black
        
        // CRITICAL: Standard font
        textView.font = NSFont.systemFont(ofSize: 14)
        
        // CRITICAL: Set text directly
        textView.string = text
        onDiagnostic("Set initial text: '\(text)'")
        
        // CRITICAL: Basic scroll view
        scrollView.documentView = textView
        scrollView.hasVerticalScroller = true
        scrollView.borderType = .noBorder
        
        // CRITICAL: Set delegate
        textView.delegate = context.coordinator
        context.coordinator.textView = textView
        context.coordinator.onDiagnostic = onDiagnostic
        
        onDiagnostic("NSTextView setup complete")
        
        return scrollView
    }
    
    func updateNSView(_ nsView: NSScrollView, context: Context) {
        guard let textView = nsView.documentView as? NSTextView else { 
            onDiagnostic("ERROR: Could not get textView in updateNSView")
            return 
        }
        
        if textView.string != text {
            onDiagnostic("Updating text from '\(textView.string)' to '\(text)'")
            textView.string = text
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, NSTextViewDelegate {
        let parent: MinimalTextEditor
        var textView: NSTextView?
        var onDiagnostic: ((String) -> Void)?
        
        init(_ parent: MinimalTextEditor) {
            self.parent = parent
        }
        
        func textDidChange(_ notification: Notification) {
            guard let textView = notification.object as? NSTextView else { return }
            
            onDiagnostic?("Text changed to: '\(textView.string)'")
            
            DispatchQueue.main.async {
                self.parent.text = textView.string
            }
        }
        
        func textViewDidChangeSelection(_ notification: Notification) {
            guard let textView = notification.object as? NSTextView else { return }
            let range = textView.selectedRange()
            onDiagnostic?("Selection changed: location=\(range.location), length=\(range.length)")
        }
    }
}
