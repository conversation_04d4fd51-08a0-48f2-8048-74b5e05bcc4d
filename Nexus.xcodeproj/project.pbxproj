// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		24C678562E3F3F8200447090 /* Markdown in Frameworks */ = {isa = PBXBuildFile; productRef = CE001A232C8A1C0000123456 /* Markdown */; };
		24C678572E3F3F8200447090 /* Highlightr in Frameworks */ = {isa = PBXBuildFile; productRef = CE001A262C8A1C1000123456 /* Highlightr */; };
		24C678582E3F3F8200447090 /* KeyboardShortcuts in Frameworks */ = {isa = PBXBuildFile; productRef = CE001A342C8A1C2000123456 /* KeyboardShortcuts */; };
		CE001A0A2C8A1B0000123456 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE001A092C8A1B0000123456 /* AppDelegate.swift */; };
		CE001A0C2C8A1B0000123456 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE001A0B2C8A1B0000123456 /* ContentView.swift */; };
		CE001A0E2C8A1B0200123456 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = CE001A0D2C8A1B0200123456 /* Assets.xcassets */; };
		CE001A112C8A1B0200123456 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = CE001A102C8A1B0200123456 /* Preview Assets.xcassets */; };
		CE001A132C8A1B0200123456 /* Persistence.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE001A122C8A1B0200123456 /* Persistence.swift */; };
		CE001A162C8A1B0200123456 /* Nexus.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = CE001A142C8A1B0200123456 /* Nexus.xcdatamodeld */; };
		CE001A272C8A1D0000123456 /* GlobalHotkeyService.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE001A282C8A1D0000123456 /* GlobalHotkeyService.swift */; };
		CE001A292C8A1E0000123456 /* SearchService.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE001A2A2C8A1E0000123456 /* SearchService.swift */; };
		CE001A2B2C8A1F0000123456 /* PlainTextEditor.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE001A2C2C8A1F0000123456 /* PlainTextEditor.swift */; };
		CE001A312C8A220000123456 /* NativeTextEditor.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE001A322C8A220000123456 /* NativeTextEditor.swift */; };
		CE001A2D2C8A200000123456 /* ClipboardService.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE001A2E2C8A200000123456 /* ClipboardService.swift */; };
		CE001A2F2C8A210000123456 /* NotesManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE001A302C8A210000123456 /* NotesManager.swift */; };
		CE001A312C8A220000123456 /* main.swift in Sources */ = {isa = PBXBuildFile; fileRef = CE001A322C8A220000123456 /* main.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		CE001A062C8A1B0000123456 /* Nexus.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Nexus.app; sourceTree = BUILT_PRODUCTS_DIR; };
		CE001A092C8A1B0000123456 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		CE001A0B2C8A1B0000123456 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		CE001A0D2C8A1B0200123456 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		CE001A102C8A1B0200123456 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		CE001A122C8A1B0200123456 /* Persistence.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Persistence.swift; sourceTree = "<group>"; };
		CE001A152C8A1B0200123456 /* Nexus.xcdatamodel */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = Nexus.xcdatamodel; sourceTree = "<group>"; };
		CE001A172C8A1B0200123456 /* Nexus.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Nexus.entitlements; sourceTree = "<group>"; };
		CE001A1E2C8A1B5000123456 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		CE001A282C8A1D0000123456 /* GlobalHotkeyService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GlobalHotkeyService.swift; sourceTree = "<group>"; };
		CE001A2A2C8A1E0000123456 /* SearchService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchService.swift; sourceTree = "<group>"; };
		CE001A2C2C8A1F0000123456 /* PlainTextEditor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlainTextEditor.swift; sourceTree = "<group>"; };
		CE001A322C8A220000123456 /* NativeTextEditor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NativeTextEditor.swift; sourceTree = "<group>"; };
		CE001A2E2C8A200000123456 /* ClipboardService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClipboardService.swift; sourceTree = "<group>"; };
		CE001A302C8A210000123456 /* NotesManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotesManager.swift; sourceTree = "<group>"; };
		CE001A322C8A220000123456 /* main.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = main.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		CE001A032C8A1B0000123456 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				24C678572E3F3F8200447090 /* Highlightr in Frameworks */,
				24C678562E3F3F8200447090 /* Markdown in Frameworks */,
				24C678582E3F3F8200447090 /* KeyboardShortcuts in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		CE001A072C8A1B0000123456 /* Products */ = {
			isa = PBXGroup;
			children = (
				CE001A062C8A1B0000123456 /* Nexus.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		CE001A082C8A1B0000123456 /* Nexus */ = {
			isa = PBXGroup;
			children = (
				CE001A1E2C8A1B5000123456 /* Info.plist */,
				CE001A322C8A220000123456 /* main.swift */,
				CE001A092C8A1B0000123456 /* AppDelegate.swift */,
				CE001A1D2C8A1B4000123456 /* Core */,
				CE001A1F2C8A1B6000123456 /* Features */,
				CE001A202C8A1B6000123456 /* UI */,
				CE001A212C8A1B6000123456 /* Models */,
				CE001A0D2C8A1B0200123456 /* Assets.xcassets */,
				CE001A172C8A1B0200123456 /* Nexus.entitlements */,
				CE001A0F2C8A1B0200123456 /* Preview Content */,
			);
			path = Nexus;
			sourceTree = "<group>";
		};
		CE001A0F2C8A1B0200123456 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				CE001A102C8A1B0200123456 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		CE001A1D2C8A1B4000123456 /* Core */ = {
			isa = PBXGroup;
			children = (
				CE001A122C8A1B0200123456 /* Persistence.swift */,
				CE001A282C8A1D0000123456 /* GlobalHotkeyService.swift */,
				CE001A2A2C8A1E0000123456 /* SearchService.swift */,
				CE001A2E2C8A200000123456 /* ClipboardService.swift */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		CE001A1F2C8A1B6000123456 /* Features */ = {
			isa = PBXGroup;
			children = (
				CE001A302C8A210000123456 /* NotesManager.swift */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		CE001A202C8A1B6000123456 /* UI */ = {
			isa = PBXGroup;
			children = (
				CE001A0B2C8A1B0000123456 /* ContentView.swift */,
				CE001A2C2C8A1F0000123456 /* PlainTextEditor.swift */,
				CE001A322C8A220000123456 /* NativeTextEditor.swift */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		CE001A212C8A1B6000123456 /* Models */ = {
			isa = PBXGroup;
			children = (
				CE001A142C8A1B0200123456 /* Nexus.xcdatamodeld */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		CE001AFD2C8A1B0000123456 = {
			isa = PBXGroup;
			children = (
				CE001A082C8A1B0000123456 /* Nexus */,
				CE001A072C8A1B0000123456 /* Products */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		CE001A052C8A1B0000123456 /* Nexus */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = CE001A1A2C8A1B0200123456 /* Build configuration list for PBXNativeTarget "Nexus" */;
			buildPhases = (
				CE001A022C8A1B0000123456 /* Sources */,
				CE001A032C8A1B0000123456 /* Frameworks */,
				CE001A042C8A1B0000123456 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Nexus;
			packageProductDependencies = (
				CE001A232C8A1C0000123456 /* Markdown */,
				CE001A262C8A1C1000123456 /* Highlightr */,
				CE001A342C8A1C2000123456 /* KeyboardShortcuts */,
			);
			productName = Nexus;
			productReference = CE001A062C8A1B0000123456 /* Nexus.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		CE001AFE2C8A1B0000123456 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1530;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					CE001A052C8A1B0000123456 = {
						CreatedOnToolsVersion = 15.3;
					};
				};
			};
			buildConfigurationList = CE001A012C8A1B0000123456 /* Build configuration list for PBXProject "Nexus" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = CE001AFD2C8A1B0000123456;
			packageReferences = (
				CE001A222C8A1C0000123456 /* XCRemoteSwiftPackageReference "swift-markdown" */,
				CE001A252C8A1C1000123456 /* XCRemoteSwiftPackageReference "Highlightr" */,
				CE001A332C8A1C2000123456 /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */,
			);
			productRefGroup = CE001A072C8A1B0000123456 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				CE001A052C8A1B0000123456 /* Nexus */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		CE001A042C8A1B0000123456 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CE001A112C8A1B0200123456 /* Preview Assets.xcassets in Resources */,
				CE001A0E2C8A1B0200123456 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		CE001A022C8A1B0000123456 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CE001A132C8A1B0200123456 /* Persistence.swift in Sources */,
				CE001A0C2C8A1B0000123456 /* ContentView.swift in Sources */,
				CE001A162C8A1B0200123456 /* Nexus.xcdatamodeld in Sources */,
				CE001A272C8A1D0000123456 /* GlobalHotkeyService.swift in Sources */,
				CE001A292C8A1E0000123456 /* SearchService.swift in Sources */,
				CE001A2B2C8A1F0000123456 /* PlainTextEditor.swift in Sources */,
				CE001A312C8A220000123456 /* NativeTextEditor.swift in Sources */,
				CE001A2D2C8A200000123456 /* ClipboardService.swift in Sources */,
				CE001A2F2C8A210000123456 /* NotesManager.swift in Sources */,
				CE001A312C8A220000123456 /* main.swift in Sources */,
				CE001A0A2C8A1B0000123456 /* AppDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		CE001A182C8A1B0200123456 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		CE001A192C8A1B0200123456 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		CE001A1B2C8A1B0200123456 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Nexus/Nexus.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"Nexus/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = Nexus/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.nexus.Nexus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		CE001A1C2C8A1B0200123456 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Nexus/Nexus.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"Nexus/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = Nexus/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.nexus.Nexus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		CE001A012C8A1B0000123456 /* Build configuration list for PBXProject "Nexus" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE001A182C8A1B0200123456 /* Debug */,
				CE001A192C8A1B0200123456 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		CE001A1A2C8A1B0200123456 /* Build configuration list for PBXNativeTarget "Nexus" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				CE001A1B2C8A1B0200123456 /* Debug */,
				CE001A1C2C8A1B0200123456 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		CE001A222C8A1C0000123456 /* XCRemoteSwiftPackageReference "swift-markdown" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/apple/swift-markdown.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.4.0;
			};
		};
		CE001A252C8A1C1000123456 /* XCRemoteSwiftPackageReference "Highlightr" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/raspu/Highlightr.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.1.0;
			};
		};
		CE001A332C8A1C2000123456 /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sindresorhus/KeyboardShortcuts.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		CE001A232C8A1C0000123456 /* Markdown */ = {
			isa = XCSwiftPackageProductDependency;
			package = CE001A222C8A1C0000123456 /* XCRemoteSwiftPackageReference "swift-markdown" */;
			productName = Markdown;
		};
		CE001A262C8A1C1000123456 /* Highlightr */ = {
			isa = XCSwiftPackageProductDependency;
			package = CE001A252C8A1C1000123456 /* XCRemoteSwiftPackageReference "Highlightr" */;
			productName = Highlightr;
		};
		CE001A342C8A1C2000123456 /* KeyboardShortcuts */ = {
			isa = XCSwiftPackageProductDependency;
			package = CE001A332C8A1C2000123456 /* XCRemoteSwiftPackageReference "KeyboardShortcuts" */;
			productName = KeyboardShortcuts;
		};
/* End XCSwiftPackageProductDependency section */

/* Begin XCVersionGroup section */
		CE001A142C8A1B0200123456 /* Nexus.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				CE001A152C8A1B0200123456 /* Nexus.xcdatamodel */,
			);
			currentVersion = CE001A152C8A1B0200123456 /* Nexus.xcdatamodel */;
			path = Nexus.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */
	};
	rootObject = CE001AFE2C8A1B0000123456 /* Project object */;
}
