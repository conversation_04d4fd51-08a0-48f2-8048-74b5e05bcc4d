#!/bin/bash

# Test script to verify text visibility fix in Nexus app
echo "🧪 Testing text visibility fix in Nexus app..."

# Check if Nexus is running
if ! pgrep -f "Nexus" > /dev/null; then
    echo "❌ Nexus app is not running. Please start it first."
    exit 1
fi

echo "✅ Nexus app is running"

# Instructions for manual testing
echo ""
echo "📋 Manual Test Instructions:"
echo "1. Click on the Nexus menu bar icon to open the app"
echo "2. Click on 'Create New Note' button or the '+' button"
echo "3. Try typing some text in the editor"
echo "4. Check if the text is visible as you type"
echo "5. Try selecting the text with your mouse"
echo "6. Check the console output below for debugging information"
echo ""

# Monitor console output for our debug messages
echo "🔍 Monitoring console output for Nexus debug messages..."
echo "Press Ctrl+C to stop monitoring"
echo ""

# Filter for our specific debug messages
log stream --predicate 'process == "Nexus"' --style compact | grep -E "(NEXUS DEBUG|📝|🎨|🎯|🖱️|🎹)"
