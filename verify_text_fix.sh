#!/bin/bash

# <PERSON>ript to verify text visibility fix by monitoring debug output
echo "🔍 Verifying text visibility fix..."

# Check if Nexus is running
if ! pgrep -f "Nexus" > /dev/null; then
    echo "❌ Nexus app is not running. Please start it first."
    exit 1
fi

echo "✅ Nexus app is running"
echo ""
echo "📋 Test Status:"
echo "1. ✅ High-contrast colors implemented"
echo "2. ✅ SwiftUI corner radius removed"
echo "3. ✅ Scroll view background cleared"
echo "4. ✅ Timer-based color protection added"
echo "5. ✅ Force display updates implemented"
echo ""

echo "🎯 Looking for debug messages that indicate text editor creation..."
echo "Please interact with the app (create a note and type) to see debug output."
echo ""

# Monitor for specific debug messages for 30 seconds
timeout 30 log stream --predicate 'process == "Nexus"' --style compact | grep -E "(NEXUS DEBUG|📝|🎨|🎯|Creating NSTextView|Text colors set|Applied attributes)" | while read line; do
    echo "✅ $line"
done

echo ""
echo "🏁 Monitoring complete. If you saw debug messages above, the text editor is being created and configured."
echo "If text is still not visible, the issue may be deeper in the rendering pipeline."
