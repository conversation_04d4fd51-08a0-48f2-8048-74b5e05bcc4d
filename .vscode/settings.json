{"swift.buildPath": "${workspaceFolder}/.build", "swift.packagePath": "${workspaceFolder}", "swift.sourcekit-lsp.serverPath": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/sourcekit-lsp", "swift.sourcekit-lsp.serverArguments": ["--log-level", "info"], "files.associations": {"*.swift": "swift"}, "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": false, "swift.autoGenerateCompileCommands": true, "swift.disableSwiftPMIntegration": false, "swift.enableSourcekitLSP": true, "swift.diagnostics": true, "swift.indexOnStartup": true, "swift.backgroundIndexing": true, "files.exclude": {"**/.build": true, "**/DerivedData": true, "**/.swiftpm": true}, "search.exclude": {"**/.build": true, "**/DerivedData": true, "**/.swiftpm": true}, "files.watcherExclude": {"**/.build/**": true, "**/DerivedData/**": true, "**/.swiftpm/**": true}}