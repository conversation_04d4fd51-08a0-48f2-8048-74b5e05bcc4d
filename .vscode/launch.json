{"version": "0.2.0", "configurations": [{"name": "Debu<PERSON> (Swift)", "type": "lldb", "request": "launch", "program": "${workspaceFolder}/.build/debug/Nexus", "args": [], "cwd": "${workspaceFolder}", "stopOnEntry": false, "environment": [], "externalConsole": false, "preLaunchTask": "Swift Build", "MIMode": "lldb"}, {"name": "Release Nexus (Swift)", "type": "lldb", "request": "launch", "program": "${workspaceFolder}/.build/release/Nexus", "args": [], "cwd": "${workspaceFolder}", "stopOnEntry": false, "environment": [], "externalConsole": false, "preLaunchTask": "Swift Build Release", "MIMode": "lldb"}, {"name": "Attach to Nexus Process", "type": "lldb", "request": "attach", "program": "${workspaceFolder}/.build/debug/Nexus", "processId": "${command:pickProcess}", "MIMode": "lldb"}]}