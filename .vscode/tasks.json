{"version": "2.0.0", "tasks": [{"label": "Swift Build", "type": "shell", "command": "swift", "args": ["build"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": {"owner": "swift", "fileLocation": "absolute", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "Swift Build Release", "type": "shell", "command": "swift", "args": ["build", "-c", "release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": {"owner": "swift", "fileLocation": "absolute", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "Swift Run", "type": "shell", "command": "swift", "args": ["run"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "dependsOn": "Swift Build"}, {"label": "Swift Clean", "type": "shell", "command": "swift", "args": ["package", "clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "Xcode Build", "type": "shell", "command": "xcodebuild", "args": ["-project", "Nexus.xcodeproj", "-scheme", "Nexus", "-configuration", "Debug", "build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": {"owner": "xcode", "fileLocation": "absolute", "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "Xcode Run", "type": "shell", "command": "open", "args": ["-a", "Nexus", ".build/debug/Nexus"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "dependsOn": "Swift Build"}]}