# Nexus VS Code Development Setup

This guide explains how to develop, build, and test the Nexus macOS application directly from VS Code, eliminating the need to constantly switch to Xcode for basic functionality testing.

## Prerequisites

### Required Software
1. **VS Code** with Swift extensions
2. **Xcode** (for toolchain and simulators)
3. **Swift** (comes with Xcode)
4. **Git** (for version control)

### Recommended VS Code Extensions
- **Swift** by Swift Server Work Group
- **CodeLLDB** for debugging
- **Swift Syntax** for syntax highlighting

## Project Structure

The project now supports dual development environments:

```
nexus/
├── Package.swift              # Swift Package Manager configuration
├── Sources/                   # SPM source structure (symlinked to Nexus/)
│   ├── Nexus/                # Main source files (symlinks)
│   └── Resources/             # Assets and resources
├── Nexus/                     # Original Xcode project structure
├── Nexus.xcodeproj/          # Xcode project (maintained for compatibility)
├── .vscode/                   # VS Code configuration
│   ├── tasks.json            # Build and run tasks
│   ├── launch.json           # Debug configurations
│   └── settings.json         # Swift language server settings
├── build.sh                   # Convenient build script
└── VS_CODE_DEVELOPMENT.md     # This documentation
```

## Setup Instructions

### 1. Configure Xcode Command Line Tools
```bash
# Ensure Xcode is properly selected
sudo xcode-select -s /Applications/Xcode.app/Contents/Developer

# Verify the setup
xcode-select -p
# Should output: /Applications/Xcode.app/Contents/Developer
```

### 2. Install VS Code Extensions
1. Open VS Code
2. Install the **Swift** extension
3. Install the **CodeLLDB** extension for debugging
4. Restart VS Code

### 3. Open Project in VS Code
```bash
cd /path/to/nexus
code .
```

## Development Workflow

### Building the Application

#### Option 1: Using VS Code Tasks (Recommended)
1. Press `Cmd+Shift+P` to open Command Palette
2. Type "Tasks: Run Task"
3. Select one of:
   - **Swift Build** - Build with SPM (may show warnings)
   - **Swift Build Release** - Release build with SPM
   - **Xcode Build** - Build with Xcode (most reliable)

#### Option 2: Using the Build Script
```bash
# Build with Xcode (recommended)
./build.sh build-xcode

# Build with SPM (may show warnings due to dependency issues)
./build.sh build-spm

# Clean all build artifacts
./build.sh clean
```

#### Option 3: Using Terminal Commands
```bash
# SPM build
swift build

# Xcode build
xcodebuild -project Nexus.xcodeproj -scheme Nexus -configuration Debug build
```

### Running the Application

#### From VS Code
1. Press `F5` or use Debug menu
2. Select "Debug Nexus (Swift)" configuration
3. The app will build and launch with debugger attached

#### Using Build Script
```bash
# Build and run with Xcode
./build.sh run-xcode

# Build and run with SPM
./build.sh run-spm
```

### Debugging

1. Set breakpoints in VS Code by clicking in the gutter
2. Press `F5` to start debugging
3. Use the Debug Console for LLDB commands
4. Variables and call stack are visible in the Debug sidebar

## Known Limitations and Workarounds

### SwiftUI Preview Macros
- **Issue**: SPM builds show errors related to SwiftUI Preview macros in dependencies
- **Impact**: Build warnings but doesn't affect core functionality
- **Workaround**: Use Xcode builds for clean compilation

### Hot Reload / SwiftUI Previews
- **Status**: Not fully supported in VS Code for macOS apps
- **Alternative**: Use rapid build-test cycles with the build script
- **Future**: May be supported as Swift tooling improves

### Core Data Models
- **Issue**: `.xcdatamodeld` files are best edited in Xcode
- **Workaround**: Use Xcode for data model changes, VS Code for Swift code

## Rapid Development Tips

### 1. Quick Build and Test Cycle
```bash
# Create an alias for quick testing
alias nexus-test="./build.sh run-xcode"

# Now you can quickly test changes
nexus-test
```

### 2. VS Code Keyboard Shortcuts
- `Cmd+Shift+B` - Run build task
- `F5` - Start debugging
- `Cmd+Shift+P` - Command palette
- `Cmd+T` - Go to symbol

### 3. File Watching
VS Code automatically detects file changes and can trigger rebuilds. The `.vscode/settings.json` is configured to exclude build directories from file watching for better performance.

### 4. Multi-Terminal Workflow
1. Terminal 1: Keep a build script running
2. Terminal 2: Git operations
3. Terminal 3: Testing and debugging

## Troubleshooting

### Build Errors
1. **"xcodebuild not found"**
   ```bash
   sudo xcode-select -s /Applications/Xcode.app/Contents/Developer
   ```

2. **SPM dependency issues**
   ```bash
   swift package clean
   swift package resolve
   ```

3. **VS Code not recognizing Swift**
   - Restart VS Code
   - Check that Swift extension is installed and enabled
   - Verify `.vscode/settings.json` has correct sourcekit-lsp path

### Performance Issues
1. **Slow indexing**: Exclude build directories in settings
2. **High CPU usage**: Disable background indexing temporarily
3. **Memory usage**: Close unused files and restart VS Code periodically

## Maintaining Xcode Compatibility

The setup maintains full Xcode compatibility:
- Original `Nexus.xcodeproj` is unchanged
- Source files are symlinked, not moved
- Both environments can be used simultaneously
- Team members can continue using Xcode exclusively

## Next Steps

1. **Test the setup** with a simple code change
2. **Customize VS Code settings** to your preferences  
3. **Set up additional tasks** for specific workflows
4. **Configure git hooks** for automated testing

For questions or issues, refer to the main project documentation or create an issue in the repository.
