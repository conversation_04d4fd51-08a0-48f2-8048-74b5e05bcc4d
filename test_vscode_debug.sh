#!/bin/bash

# Test VS Code debugging capabilities
# This script validates that the debugging configuration works

set -e

echo "🔍 Testing VS Code Debugging Setup"
echo "=================================="

# Test 1: Verify launch.json exists and is valid
echo "1. Testing launch.json configuration..."
if [ -f ".vscode/launch.json" ]; then
    echo "✅ launch.json exists"
    
    # Check if it contains the expected configurations
    if grep -q "Debug Nexus (Swift)" .vscode/launch.json; then
        echo "✅ Debug configuration found"
    else
        echo "❌ Debug configuration missing"
        exit 1
    fi
    
    if grep -q "lldb" .vscode/launch.json; then
        echo "✅ LLDB debugger configured"
    else
        echo "❌ LLDB debugger not configured"
        exit 1
    fi
else
    echo "❌ launch.json not found"
    exit 1
fi

# Test 2: Verify tasks.json exists and is valid
echo "2. Testing tasks.json configuration..."
if [ -f ".vscode/tasks.json" ]; then
    echo "✅ tasks.json exists"
    
    # Check for build tasks
    if grep -q "Swift Build" .vscode/tasks.json; then
        echo "✅ Swift build task found"
    else
        echo "❌ Swift build task missing"
        exit 1
    fi
    
    if grep -q "Xcode Build" .vscode/tasks.json; then
        echo "✅ Xcode build task found"
    else
        echo "❌ Xcode build task missing"
        exit 1
    fi
else
    echo "❌ tasks.json not found"
    exit 1
fi

# Test 3: Verify settings.json exists and has Swift configuration
echo "3. Testing settings.json configuration..."
if [ -f ".vscode/settings.json" ]; then
    echo "✅ settings.json exists"
    
    if grep -q "sourcekit-lsp" .vscode/settings.json; then
        echo "✅ SourceKit-LSP configured"
    else
        echo "❌ SourceKit-LSP not configured"
        exit 1
    fi
else
    echo "❌ settings.json not found"
    exit 1
fi

# Test 4: Check if LLDB is available
echo "4. Testing LLDB availability..."
if command -v lldb >/dev/null 2>&1; then
    echo "✅ LLDB found: $(lldb --version | head -1)"
else
    echo "❌ LLDB not found"
    exit 1
fi

# Test 5: Test build task execution
echo "5. Testing build task execution..."
if ./build.sh build-xcode >/dev/null 2>&1; then
    echo "✅ Xcode build task works"
else
    echo "❌ Xcode build task failed"
    exit 1
fi

# Test 6: Verify debug executable exists
echo "6. Testing debug executable..."
app_path=$(find ~/Library/Developer/Xcode/DerivedData -name "Nexus.app" -type d 2>/dev/null | grep "Build/Products/Debug/Nexus.app" | grep -v "Index.noindex" | head -1)

if [ -n "$app_path" ] && [ -x "$app_path/Contents/MacOS/Nexus" ]; then
    echo "✅ Debug executable found and is executable"
    echo "   Path: $app_path/Contents/MacOS/Nexus"
else
    echo "❌ Debug executable not found or not executable"
    exit 1
fi

echo ""
echo "🎉 All VS Code debugging tests passed!"
echo ""
echo "VS Code Debugging Setup Summary:"
echo "- ✅ Launch configurations ready"
echo "- ✅ Build tasks configured"
echo "- ✅ Swift language server setup"
echo "- ✅ LLDB debugger available"
echo "- ✅ Debug executable built and ready"
echo ""
echo "To test debugging in VS Code:"
echo "1. Open VS Code: code ."
echo "2. Set a breakpoint in any Swift file"
echo "3. Press F5 or use Debug menu"
echo "4. Select 'Debug Nexus (Swift)' configuration"
echo "5. The app should launch with debugger attached"
echo ""
echo "Note: For GUI testing, you'll need to interact with the menu bar icon"
echo "and test the window movement and text editing features manually."
