#!/bin/bash

# Test script to verify text input functionality in Nexus
# This script will launch the app and monitor debug output to verify text input

echo "🧪 Testing Nexus Text Input Functionality"
echo "=========================================="

# Function to check if Nexus is running
check_nexus_running() {
    pgrep -f "Nexus.app" > /dev/null
    return $?
}

# Function to get Nexus PID
get_nexus_pid() {
    pgrep -f "Nexus.app" | head -1
}

# Function to monitor Nexus console output
monitor_nexus_output() {
    local pid=$1
    echo "📊 Monitoring Nexus debug output (PID: $pid)..."
    echo "   Looking for text input related logs..."
    
    # Monitor system logs for Nexus process
    log stream --predicate "process == 'Nexus'" --level debug --style compact &
    local log_pid=$!
    
    echo "   Log monitoring started (PID: $log_pid)"
    echo "   Press Ctrl+C to stop monitoring"
    
    # Wait for user to stop
    trap "kill $log_pid 2>/dev/null; exit 0" INT
    wait $log_pid
}

# Function to test application state
test_application_state() {
    echo "🔍 Testing Application State"
    echo "----------------------------"
    
    if check_nexus_running; then
        local pid=$(get_nexus_pid)
        echo "✅ Nexus is running (PID: $pid)"
        
        # Check if it's a background app
        local app_info=$(osascript -e "tell application \"System Events\" to tell process \"Nexus\" to get {background only, frontmost}" 2>/dev/null)
        if [[ $? -eq 0 ]]; then
            echo "✅ Application properties: $app_info"
        else
            echo "⚠️  Cannot access application properties (accessibility permissions needed)"
        fi
        
        # Check memory usage
        local memory=$(ps -o rss= -p $pid 2>/dev/null)
        if [[ -n "$memory" ]]; then
            echo "📊 Memory usage: ${memory} KB"
        fi
        
        return 0
    else
        echo "❌ Nexus is not running"
        return 1
    fi
}

# Function to provide manual testing instructions
provide_manual_test_instructions() {
    echo ""
    echo "📋 Manual Testing Instructions"
    echo "=============================="
    echo ""
    echo "Since this is a GUI application, please perform the following manual tests:"
    echo ""
    echo "1. 🔍 LOCATE THE MENU BAR ICON:"
    echo "   - Look in the top-right area of your screen (menu bar)"
    echo "   - Find the document icon (📄) for Nexus"
    echo "   - If you don't see it, the app may not have started properly"
    echo ""
    echo "2. 🖱️  OPEN THE APPLICATION:"
    echo "   - Left-click the Nexus menu bar icon"
    echo "   - This should open a popover window"
    echo "   - OR right-click and select 'Open in Window' for detached mode"
    echo ""
    echo "3. 📝 TEST TEXT INPUT:"
    echo "   - Click on the 'Notes' tab if not already selected"
    echo "   - Click 'Create New Note' or select an existing note"
    echo "   - Click in the text editor area (large text area)"
    echo "   - Try typing some text"
    echo ""
    echo "4. ✅ VERIFY RESULTS:"
    echo "   - Can you see the text as you type?"
    echo "   - Is the cursor visible?"
    echo "   - Can you select text with mouse/keyboard?"
    echo "   - Does copy/paste work (Cmd+C, Cmd+V)?"
    echo ""
    echo "5. 🐛 IF TEXT INPUT FAILS:"
    echo "   - Note exactly what happens (or doesn't happen)"
    echo "   - Check if cursor appears when clicking in text area"
    echo "   - Try both popover and detached window modes"
    echo "   - Look for any error messages"
    echo ""
    echo "6. 📊 MONITOR DEBUG OUTPUT:"
    echo "   - Run this script with 'monitor' option to see debug logs"
    echo "   - Look for messages starting with 🔨, 🎯, 📝, 🖱️, 🎹"
    echo ""
}

# Main script logic
case "${1:-test}" in
    "monitor")
        if check_nexus_running; then
            monitor_nexus_output $(get_nexus_pid)
        else
            echo "❌ Nexus is not running. Start it first with: ./build.sh run-xcode"
            exit 1
        fi
        ;;
    "test")
        test_application_state
        provide_manual_test_instructions
        ;;
    "help")
        echo "Usage: $0 [test|monitor|help]"
        echo ""
        echo "Commands:"
        echo "  test    - Check application state and provide testing instructions (default)"
        echo "  monitor - Monitor debug output from running Nexus application"
        echo "  help    - Show this help message"
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
