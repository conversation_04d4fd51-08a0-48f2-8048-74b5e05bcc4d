import AppKit
import SwiftUI

// COMPLETE REWRITE: Native AppKit text editor that bypasses SwiftUI entirely
class NativeTextEditorWindow: NSWindow {
    private let textView: NSTextView
    private let scrollView: NSScrollView
    private var onTextChange: ((String) -> Void)?
    private var currentText: String = ""
    
    init() {
        // Create scroll view and text view
        scrollView = NSScrollView()
        textView = NSTextView()
        
        // Initialize window
        super.init(
            contentRect: NSRect(x: 0, y: 0, width: 600, height: 400),
            styleMask: [.titled, .closable, .resizable, .miniaturizable],
            backing: .buffered,
            defer: false
        )
        
        setupTextView()
        setupWindow()
    }
    
    private func setupTextView() {
        // FUNDAMENTAL: Basic text view setup
        textView.isEditable = true
        textView.isSelectable = true
        textView.isRichText = false
        textView.allowsUndo = true
        
        // FUNDAMENTAL: Use high-contrast, always-visible colors
        textView.textColor = .black
        textView.backgroundColor = .white
        textView.insertionPointColor = .black
        
        // FUNDAMENTAL: Use clear, readable font
        let font = NSFont.monospacedSystemFont(ofSize: 14, weight: .regular)
        textView.font = font
        
        // FUNDAMENTAL: Set typing attributes
        textView.typingAttributes = [
            .font: font,
            .foregroundColor: NSColor.black
        ]
        
        // FUNDAMENTAL: Disable automatic features
        textView.isAutomaticQuoteSubstitutionEnabled = false
        textView.isAutomaticDashSubstitutionEnabled = false
        textView.isAutomaticTextReplacementEnabled = false
        textView.isAutomaticSpellingCorrectionEnabled = false
        textView.isAutomaticLinkDetectionEnabled = false
        textView.smartInsertDeleteEnabled = false
        
        // FUNDAMENTAL: Text container setup
        textView.textContainerInset = NSSize(width: 10, height: 10)
        textView.isVerticallyResizable = true
        textView.isHorizontallyResizable = false
        textView.maxSize = NSSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude)
        
        if let container = textView.textContainer {
            container.widthTracksTextView = true
            container.heightTracksTextView = false
        }
        
        // FUNDAMENTAL: Scroll view setup
        scrollView.documentView = textView
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true
        scrollView.borderType = .bezelBorder
        
        // Set delegate
        textView.delegate = self
    }
    
    private func setupWindow() {
        title = "Text Editor"
        contentView = scrollView
        center()
        
        // Make text view first responder when window opens
        makeFirstResponder(textView)
    }
    
    func setText(_ text: String, onChange: @escaping (String) -> Void) {
        currentText = text
        onTextChange = onChange
        textView.string = text
        
        // Force focus
        makeFirstResponder(textView)
    }
    
    func getText() -> String {
        return textView.string
    }
}

extension NativeTextEditorWindow: NSTextViewDelegate {
    func textDidChange(_ notification: Notification) {
        let newText = textView.string
        if newText != currentText {
            currentText = newText
            onTextChange?(newText)
        }
    }
}

// SwiftUI wrapper for the native text editor
struct NativeTextEditorView: View {
    @Binding var text: String
    @State private var editorWindow: NativeTextEditorWindow?
    @State private var isEditorOpen = false
    
    var body: some View {
        VStack(spacing: 12) {
            // Preview of current text
            ScrollView {
                Text(text.isEmpty ? "No content" : text)
                    .font(.system(size: 14, family: .monospaced))
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding()
                    .background(Color(NSColor.textBackgroundColor))
                    .overlay(
                        RoundedRectangle(cornerRadius: 4)
                            .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                    )
            }
            .frame(maxHeight: 200)
            
            // Edit button
            Button(action: openEditor) {
                HStack {
                    Image(systemName: "pencil")
                    Text("Edit in Native Editor")
                }
            }
            .buttonStyle(.borderedProminent)
            
            if isEditorOpen {
                Text("Native editor is open")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private func openEditor() {
        if editorWindow == nil {
            editorWindow = NativeTextEditorWindow()
        }
        
        guard let window = editorWindow else { return }
        
        window.setText(text) { newText in
            DispatchQueue.main.async {
                self.text = newText
            }
        }
        
        window.makeKeyAndOrderFront(nil)
        isEditorOpen = true
        
        // Monitor window closing
        NotificationCenter.default.addObserver(
            forName: NSWindow.willCloseNotification,
            object: window,
            queue: .main
        ) { _ in
            self.isEditorOpen = false
        }
    }
}
