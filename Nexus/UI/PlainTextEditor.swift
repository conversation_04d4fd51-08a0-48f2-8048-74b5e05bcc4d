import SwiftUI
import AppKit

// COMPLETE REWRITE: Simplified, robust text editor that avoids SwiftUI interference
struct PlainTextEditor: NSViewRepresentable {
    @Binding var text: String
    var isMarkdownEnabled: Bool = false
    var shouldFocusOnAppear: Bool = false

    func makeNSView(context: Context) -> NSScrollView {
        let scrollView = NSScrollView()
        let textView = NSTextView()

        // FUNDAMENTAL: Ensure text view gets all events
        textView.isEditable = true
        textView.isSelectable = true
        textView.isRichText = false
        textView.allowsUndo = true

        // FUNDAMENTAL: Use system colors that always work
        textView.textColor = .labelColor
        textView.backgroundColor = .textBackgroundColor
        textView.insertionPointColor = .labelColor

        // FUNDAMENTAL: Use system font
        let font = NSFont.systemFont(ofSize: 14)
        textView.font = font

        // FUNDAMENTAL: Set typing attributes immediately
        textView.typingAttributes = [
            .font: font,
            .foregroundColor: NSColor.labelColor
        ]

        // FUNDAMENTAL: Disable all automatic features that could interfere
        textView.isAutomaticQuoteSubstitutionEnabled = false
        textView.isAutomaticDashSubstitutionEnabled = false
        textView.isAutomaticTextReplacementEnabled = false
        textView.isAutomaticSpellingCorrectionEnabled = false
        textView.isAutomaticLinkDetectionEnabled = false
        textView.isAutomaticDataDetectionEnabled = false
        textView.smartInsertDeleteEnabled = false

        // FUNDAMENTAL: Basic text container setup
        textView.textContainerInset = NSSize(width: 8, height: 8)
        textView.isVerticallyResizable = true
        textView.isHorizontallyResizable = false
        textView.maxSize = NSSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude)

        // FUNDAMENTAL: Configure text container
        if let container = textView.textContainer {
            container.widthTracksTextView = true
            container.heightTracksTextView = false
        }

        // FUNDAMENTAL: Basic scroll view setup
        scrollView.documentView = textView
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true
        scrollView.borderType = .noBorder

        // FUNDAMENTAL: Set text using the simplest possible method
        textView.string = text

        // FUNDAMENTAL: Set delegate and store reference
        textView.delegate = context.coordinator
        context.coordinator.textView = textView

        // FUNDAMENTAL: Handle focus if requested
        if shouldFocusOnAppear {
            DispatchQueue.main.async {
                textView.window?.makeFirstResponder(textView)
            }
        }

        return scrollView
    }

    func updateNSView(_ nsView: NSScrollView, context: Context) {
        guard let textView = nsView.documentView as? NSTextView else { return }

        // SIMPLIFIED: Only update if text changed, use simplest method
        if textView.string != text {
            let selection = textView.selectedRange()
            textView.string = text

            // Restore selection if valid
            let maxLength = text.count
            if selection.location <= maxLength {
                let safeLocation = min(selection.location, maxLength)
                textView.setSelectedRange(NSRange(location: safeLocation, length: 0))
            }
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
}

// SIMPLIFIED: Minimal coordinator that doesn't interfere with text rendering
extension PlainTextEditor {
    class Coordinator: NSObject, NSTextViewDelegate {
        let parent: PlainTextEditor
        var textView: NSTextView?

        init(_ parent: PlainTextEditor) {
            self.parent = parent
        }

        func textDidChange(_ notification: Notification) {
            guard let textView = notification.object as? NSTextView else { return }

            // SIMPLIFIED: Just update the binding, no complex attribute management
            let newText = textView.string
            if parent.text != newText {
                DispatchQueue.main.async {
                    self.parent.text = newText
                }
            }
        }
    }
}
