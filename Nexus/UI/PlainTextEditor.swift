import SwiftUI
import AppKit

struct PlainTextEditor: NSViewRepresentable {
    @Binding var text: String
    var isMarkdownEnabled: Bool = false
    var shouldFocusOnAppear: Bool = false

    func makeNSView(context: Context) -> NSScrollView {
        let scrollView = NSScrollView()
        let textView = NSTextView()

        // CRITICAL: Configure text view for proper interaction
        textView.isRichText = false
        textView.isEditable = true
        textView.isSelectable = true
        textView.allowsUndo = true
        textView.usesFindBar = true

        // CRITICAL: Font and color configuration for visibility
        let font = NSFont.monospacedSystemFont(ofSize: 14, weight: .regular)
        textView.font = font

        // CRITICAL: Use contrasting colors that work in all appearances
        textView.textColor = NSColor.controlTextColor
        textView.backgroundColor = NSColor.textBackgroundColor
        textView.insertionPointColor = NSColor.controlTextColor

        // CRITICAL: Set typing attributes to ensure new text is visible
        textView.typingAttributes = [
            .font: font,
            .foregroundColor: NSColor.controlTextColor
        ]

        // Disable problematic auto-formatting
        textView.isAutomaticQuoteSubstitutionEnabled = false
        textView.isAutomaticDashSubstitutionEnabled = false
        textView.isAutomaticTextReplacementEnabled = false
        textView.isAutomaticSpellingCorrectionEnabled = false
        textView.smartInsertDeleteEnabled = false
        textView.isAutomaticLinkDetectionEnabled = false
        textView.isAutomaticDataDetectionEnabled = false

        // Set padding
        textView.textContainerInset = NSSize(width: 12, height: 12)

        // CRITICAL: Configure scroll view properly
        scrollView.documentView = textView
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true
        scrollView.borderType = .noBorder

        // CRITICAL: Configure text container for proper text flow
        if let textContainer = textView.textContainer {
            textContainer.containerSize = NSSize(width: scrollView.contentSize.width, height: CGFloat.greatestFiniteMagnitude)
            textContainer.widthTracksTextView = true
            textContainer.heightTracksTextView = false
        }

        textView.isVerticallyResizable = true
        textView.isHorizontallyResizable = false
        textView.maxSize = NSSize(width: CGFloat.greatestFiniteMagnitude, height: CGFloat.greatestFiniteMagnitude)

        // CRITICAL: Set initial text with proper attributes
        setTextWithVisibleAttributes(textView: textView, text: text)

        // Set delegate
        textView.delegate = context.coordinator

        // Store reference for coordinator
        context.coordinator.textView = textView

        // Handle focus
        if shouldFocusOnAppear {
            DispatchQueue.main.async {
                textView.window?.makeFirstResponder(textView)
            }
        }

        return scrollView
    }
    
    func updateNSView(_ nsView: NSScrollView, context: Context) {
        guard let textView = nsView.documentView as? NSTextView else { return }

        // Only update if text actually changed to avoid cursor jumping
        if textView.string != text {
            let currentSelection = textView.selectedRange()

            // CRITICAL: Use setTextWithVisibleAttributes to ensure text is visible
            setTextWithVisibleAttributes(textView: textView, text: text)

            // Restore cursor position
            let maxLength = text.count
            let safeLocation = min(currentSelection.location, maxLength)
            textView.setSelectedRange(NSRange(location: safeLocation, length: 0))
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    // MARK: - Helper Methods

    private func setTextWithVisibleAttributes(textView: NSTextView, text: String) {
        let font = NSFont.monospacedSystemFont(ofSize: 14, weight: .regular)
        let textColor = NSColor.controlTextColor

        // Create attributed string with visible attributes
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: textColor
        ]

        let attributedString = NSAttributedString(string: text, attributes: attributes)

        // CRITICAL: Set to textStorage to ensure proper attribute application
        textView.textStorage?.setAttributedString(attributedString)

        // CRITICAL: Ensure text view colors are correct
        textView.textColor = textColor
        textView.insertionPointColor = textColor

        // CRITICAL: Update typing attributes for new text
        textView.typingAttributes = attributes

        // Force display update
        textView.needsDisplay = true
    }
}

extension PlainTextEditor {
    class Coordinator: NSObject, NSTextViewDelegate {
        let parent: PlainTextEditor
        var textView: NSTextView?

        init(_ parent: PlainTextEditor) {
            self.parent = parent
        }

        func textDidChange(_ notification: Notification) {
            guard let textView = notification.object as? NSTextView else { return }

            // CRITICAL: Ensure text remains visible after changes
            ensureTextVisibility(textView: textView)

            // Update parent binding
            let newText = textView.string
            if parent.text != newText {
                DispatchQueue.main.async {
                    self.parent.text = newText
                }
            }
        }

        func textViewDidChangeSelection(_ notification: Notification) {
            guard let textView = notification.object as? NSTextView else { return }

            // CRITICAL: Ensure text remains visible during selection changes
            ensureTextVisibility(textView: textView)
        }

        private func ensureTextVisibility(textView: NSTextView) {
            let font = NSFont.monospacedSystemFont(ofSize: 14, weight: .regular)
            let textColor = NSColor.controlTextColor

            // Ensure all text has visible attributes
            let fullRange = NSRange(location: 0, length: textView.string.count)
            let attributes: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: textColor
            ]

            // Apply attributes to existing text
            textView.textStorage?.addAttributes(attributes, range: fullRange)

            // Ensure text view properties are correct
            textView.textColor = textColor
            textView.insertionPointColor = textColor
            textView.typingAttributes = attributes
        }
    }
}
