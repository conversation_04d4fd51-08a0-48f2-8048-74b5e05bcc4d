import Foundation
import CoreData
import SwiftUI

class NotesManager: ObservableObject {
    static let shared = NotesManager()

    @Published var notes: [Note] = []
    @Published var folders: [Folder] = []
    @Published var selectedNote: Note?
    @Published var selectedFolder: Folder?
    @Published var lastError: String?
    
    private var context: NSManagedObjectContext {
        PersistenceController.shared.container.viewContext
    }
    
    init() {
        loadNotes()
        loadFolders()

        // For testing: Auto-select first note if available
        if !notes.isEmpty {
            selectedNote = notes.first
            NSLog("📝 NEXUS DEBUG: NotesManager - Auto-selected first note for testing: \(notes.first?.title ?? "Untitled")")
        }
    }
    
    // MARK: - Notes Management

    /// Creates a new empty note and immediately selects it for editing
    func createNewNote() -> Note {
        let note = Note(context: context)
        note.uuid = UUID()
        note.title = nil  // Start with no title
        note.content = ""  // Start with empty content
        note.creationDate = Date()
        note.modifiedDate = Date()
        note.isMarkdownEnabled = false

        // Add to selected folder if one exists
        if let folder = selectedFolder {
            note.addToFolders(folder)
        }

        // Save immediately
        saveNote(note)

        // Select the new note
        selectedNote = note

        // Refresh notes list
        loadNotes()

        return note
    }

    /// Updates note content - simple and direct
    func updateNoteContent(_ note: Note, content: String) {
        guard note.content != content else { return }

        note.content = content
        note.modifiedDate = Date()

        // Don't save immediately - let the UI handle debounced saving
    }

    /// Updates note title - simple and direct
    func updateNoteTitle(_ note: Note, title: String) {
        let newTitle = title.isEmpty ? nil : title
        guard note.title != newTitle else { return }

        note.title = newTitle
        note.modifiedDate = Date()

        // Save title changes immediately
        saveNote(note)
    }

    /// Saves a specific note to Core Data
    func saveNote(_ note: Note) {
        do {
            try context.save()
        } catch {
            print("Failed to save note: \(error)")
            // Set error for UI display
            DispatchQueue.main.async {
                self.lastError = "Failed to save note: \(error.localizedDescription)"
            }
        }
    }

    /// Saves all pending changes
    func saveAllChanges() {
        guard context.hasChanges else { return }

        do {
            try context.save()
        } catch {
            print("Failed to save changes: \(error)")
            DispatchQueue.main.async {
                self.lastError = "Failed to save changes: \(error.localizedDescription)"
            }
        }
    }

    func deleteNote(_ note: Note) {
        context.delete(note)
        saveAllChanges()
        loadNotes()

        if selectedNote == note {
            selectedNote = nil
        }
    }
    
    func toggleMarkdown(for note: Note) {
        note.isMarkdownEnabled.toggle()
        note.modifiedDate = Date()
        saveAllChanges()

        objectWillChange.send()
    }
    
    func duplicateNote(_ note: Note) -> Note {
        let newNote = Note(context: context)
        newNote.uuid = UUID()
        newNote.title = (note.title ?? "Untitled") + " Copy"
        newNote.content = note.content ?? ""
        newNote.creationDate = Date()
        newNote.modifiedDate = Date()
        newNote.isMarkdownEnabled = note.isMarkdownEnabled

        // Add to same folders as original
        for folder in note.folders ?? [] {
            if let folder = folder as? Folder {
                newNote.addToFolders(folder)
            }
        }

        saveNote(newNote)
        loadNotes()

        return newNote
    }
    
    // MARK: - Folders Management
    
    func createFolder(name: String, parent: Folder? = nil) -> Folder {
        let folder = Folder(context: context)
        folder.uuid = UUID()
        folder.name = name
        folder.creationDate = Date()
        folder.parentFolder = parent

        saveAllChanges()
        loadFolders()

        return folder
    }
    
    func updateFolder(_ folder: Folder, name: String) {
        folder.name = name
        saveAllChanges()

        objectWillChange.send()
    }
    
    func deleteFolder(_ folder: Folder) {
        // Move notes to parent folder or remove folder association
        if let notes = folder.notes as? Set<Note> {
            for note in notes {
                note.removeFromFolders(folder)
                if let parentFolder = folder.parentFolder {
                    note.addToFolders(parentFolder)
                }
            }
        }
        
        // Move subfolders to parent
        if let subfolders = folder.subfolders as? Set<Folder> {
            for subfolder in subfolders {
                subfolder.parentFolder = folder.parentFolder
            }
        }
        
        context.delete(folder)
        saveAllChanges()
        loadFolders()
        
        if selectedFolder == folder {
            selectedFolder = nil
        }
    }
    
    func moveNote(_ note: Note, to folder: Folder?) {
        // Remove from all current folders
        if let currentFolders = note.folders as? Set<Folder> {
            for currentFolder in currentFolders {
                note.removeFromFolders(currentFolder)
            }
        }
        
        // Add to new folder if specified
        if let folder = folder {
            note.addToFolders(folder)
        }
        
        note.modifiedDate = Date()
        saveAllChanges()

        objectWillChange.send()
    }
    
    // MARK: - Data Loading
    
    private func loadNotes() {
        let request = NSFetchRequest<Note>(entityName: "Note")
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \Note.modifiedDate, ascending: false)
        ]
        
        do {
            notes = try context.fetch(request)
            NSLog("📝 NEXUS DEBUG: NotesManager - Loaded \(notes.count) notes")
        } catch {
            NSLog("❌ NEXUS DEBUG: NotesManager - Failed to load notes: \(error)")
            notes = []
        }
    }
    
    private func loadFolders() {
        let request = NSFetchRequest<Folder>(entityName: "Folder")
        request.sortDescriptors = [
            NSSortDescriptor(keyPath: \Folder.name, ascending: true)
        ]
        
        do {
            folders = try context.fetch(request)
        } catch {
            print("Failed to load folders: \(error)")
            folders = []
        }
    }
    
    // MARK: - Error Handling

    func dismissError() {
        DispatchQueue.main.async {
            self.lastError = nil
        }
    }
    
    // MARK: - Utility Methods
    
    func getNotesInFolder(_ folder: Folder?) -> [Note] {
        if let folder = folder {
            return folder.notesArray
        } else {
            // Return notes not in any folder
            return notes.filter { note in
                (note.folders as? Set<Folder>)?.isEmpty ?? true
            }
        }
    }
    
    func getRootFolders() -> [Folder] {
        return folders.filter { $0.parentFolder == nil }
    }
    
    func searchNotes(query: String) -> [Note] {
        guard !query.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return notes
        }
        
        let lowercaseQuery = query.lowercased()
        
        return notes.filter { note in
            let titleMatch = note.title?.lowercased().contains(lowercaseQuery) ?? false
            let contentMatch = note.content?.lowercased().contains(lowercaseQuery) ?? false
            return titleMatch || contentMatch
        }
    }
    
    func getRecentNotes(limit: Int = 10) -> [Note] {
        return Array(notes.prefix(limit))
    }
}