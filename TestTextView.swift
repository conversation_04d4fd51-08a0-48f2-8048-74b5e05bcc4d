import SwiftUI
import AppKit

// DIAGNOSTIC: Minimal NSTextView test to isolate the problem
struct TestTextView: View {
    @State private var testText = "Initial test text"
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Diagnostic Text Editor Test")
                .font(.headline)
            
            Text("Current text: '\(testText)'")
                .foregroundColor(.secondary)
            
            // Test 1: Pure NSTextView without any SwiftUI interference
            MinimalTextEditor(text: $testText)
                .frame(height: 200)
                .border(Color.red, width: 2)
            
            // Test 2: Standard SwiftUI TextEditor for comparison
            TextEditor(text: $testText)
                .frame(height: 100)
                .border(Color.blue, width: 2)
            
            But<PERSON>("Reset Text") {
                testText = "Reset at \(Date())"
            }
        }
        .padding()
        .frame(width: 500, height: 400)
    }
}

// DIAGNOSTIC: Absolute minimal NSTextView implementation
struct MinimalTextEditor: NSViewRepresentable {
    @Binding var text: String
    
    func makeNSView(context: Context) -> NSScrollView {
        let scrollView = NSScrollView()
        let textView = NSTextView()
        
        // DIAGNOSTIC: Minimal configuration - only essentials
        textView.isEditable = true
        textView.isSelectable = true
        textView.isRichText = false
        
        // DIAGNOSTIC: Explicit, simple colors
        textView.textColor = .black
        textView.backgroundColor = .white
        textView.insertionPointColor = .black
        
        // DIAGNOSTIC: Simple font
        textView.font = NSFont.systemFont(ofSize: 14)
        
        // DIAGNOSTIC: Set initial text directly
        textView.string = text
        
        // DIAGNOSTIC: Basic scroll view setup
        scrollView.documentView = textView
        scrollView.hasVerticalScroller = true
        
        // DIAGNOSTIC: Set delegate
        textView.delegate = context.coordinator
        
        // DIAGNOSTIC: Store reference
        context.coordinator.textView = textView
        
        // DIAGNOSTIC: Force first responder
        DispatchQueue.main.async {
            textView.window?.makeFirstResponder(textView)
        }
        
        return scrollView
    }
    
    func updateNSView(_ nsView: NSScrollView, context: Context) {
        guard let textView = nsView.documentView as? NSTextView else { return }
        
        // DIAGNOSTIC: Only update if different
        if textView.string != text {
            textView.string = text
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, NSTextViewDelegate {
        let parent: MinimalTextEditor
        var textView: NSTextView?
        
        init(_ parent: MinimalTextEditor) {
            self.parent = parent
        }
        
        func textDidChange(_ notification: Notification) {
            guard let textView = notification.object as? NSTextView else { return }
            
            // DIAGNOSTIC: Simple text update
            DispatchQueue.main.async {
                self.parent.text = textView.string
            }
        }
    }
}

// DIAGNOSTIC: Test window for standalone testing
struct TestWindow: View {
    var body: some View {
        TestTextView()
    }
}

#Preview {
    TestWindow()
}
