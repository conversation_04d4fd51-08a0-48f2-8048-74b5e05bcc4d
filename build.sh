#!/bin/bash

# Nexus Build Script for VS Code Development
# This script provides various build and run options for the Nexus application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to build with Swift Package Manager
build_spm() {
    print_status "Building Nexus with Swift Package Manager..."
    
    if swift build; then
        print_success "SPM build completed successfully"
        return 0
    else
        print_warning "SPM build failed (likely due to SwiftUI Preview macros in dependencies)"
        print_status "This is a known limitation but doesn't affect the core functionality"
        return 1
    fi
}

# Function to build with Xcode
build_xcode() {
    print_status "Building Nexus with Xcode..."

    # Check if Xcode is properly configured
    local xcode_path=$(xcode-select -p 2>/dev/null)
    if [[ "$xcode_path" == *"CommandLineTools"* ]]; then
        print_warning "Xcode Command Line Tools detected instead of full Xcode"
        print_status "Attempting to find Xcode installation..."

        if [ -d "/Applications/Xcode.app" ]; then
            print_status "Found Xcode at /Applications/Xcode.app"
            print_status "You may need to run: sudo xcode-select -s /Applications/Xcode.app/Contents/Developer"
        else
            print_error "Full Xcode installation not found. Please install Xcode from the App Store."
            return 1
        fi
    fi

    if ! command_exists xcodebuild; then
        print_error "xcodebuild not found. Please install Xcode."
        return 1
    fi

    if xcodebuild -project Nexus.xcodeproj -scheme Nexus -configuration Debug build; then
        print_success "Xcode build completed successfully"
        return 0
    else
        print_error "Xcode build failed"
        return 1
    fi
}

# Function to run the application
run_app() {
    local build_method="$1"

    if [ "$build_method" = "xcode" ]; then
        print_status "Running Nexus (Xcode build)..."
        # Find the built app in DerivedData - prioritize Build/Products over Index.noindex
        local app_path=$(find ~/Library/Developer/Xcode/DerivedData -name "Nexus.app" -type d 2>/dev/null | grep "Build/Products/Debug/Nexus.app" | grep -v "Index.noindex" | head -1)

        # If not found, try any Nexus.app with executable
        if [ -z "$app_path" ]; then
            local all_apps=$(find ~/Library/Developer/Xcode/DerivedData -name "Nexus.app" -type d 2>/dev/null)
            for app in $all_apps; do
                if [ -x "$app/Contents/MacOS/Nexus" ]; then
                    app_path="$app"
                    break
                fi
            done
        fi

        if [ -n "$app_path" ]; then
            print_status "Found app at: $app_path"
            open "$app_path"
            print_success "Nexus launched successfully"
        else
            print_error "Could not find executable Nexus.app in DerivedData. Try building first."
            print_status "Searching for any Nexus.app files..."
            find ~/Library/Developer/Xcode/DerivedData -name "Nexus.app" -type d 2>/dev/null | head -5
            return 1
        fi
    else
        print_status "Running Nexus (SPM build)..."
        if [ -f ".build/debug/Nexus" ]; then
            ./.build/debug/Nexus &
            print_success "Nexus launched successfully"
        else
            print_error "Nexus executable not found. Try building first."
            return 1
        fi
    fi
}

# Function to clean build artifacts
clean() {
    print_status "Cleaning build artifacts..."
    
    # Clean SPM artifacts
    if [ -d ".build" ]; then
        rm -rf .build
        print_status "Removed .build directory"
    fi
    
    # Clean Xcode artifacts
    if command_exists xcodebuild; then
        xcodebuild -project Nexus.xcodeproj -scheme Nexus clean >/dev/null 2>&1
        print_status "Cleaned Xcode build artifacts"
    fi
    
    print_success "Clean completed"
}

# Function to setup development environment
setup_env() {
    print_status "Setting up development environment..."

    # Check if Xcode is installed
    if [ ! -d "/Applications/Xcode.app" ]; then
        print_error "Xcode not found. Please install Xcode from the App Store."
        return 1
    fi

    # Configure xcode-select
    local current_path=$(xcode-select -p 2>/dev/null)
    if [[ "$current_path" == *"CommandLineTools"* ]]; then
        print_status "Configuring Xcode developer directory..."
        if sudo xcode-select -s /Applications/Xcode.app/Contents/Developer; then
            print_success "Xcode developer directory configured"
        else
            print_error "Failed to configure Xcode developer directory"
            return 1
        fi
    else
        print_success "Xcode developer directory already configured"
    fi

    # Verify setup
    if command_exists xcodebuild && command_exists swift; then
        print_success "Development environment setup complete"
        print_status "You can now use 'build-xcode' or 'run-xcode' commands"
    else
        print_error "Setup verification failed"
        return 1
    fi
}

# Function to show help
show_help() {
    echo "Nexus Build Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup         Setup development environment (configure Xcode)"
    echo "  build-spm     Build using Swift Package Manager"
    echo "  build-xcode   Build using Xcode"
    echo "  run-spm       Build and run using SPM"
    echo "  run-xcode     Build and run using Xcode"
    echo "  clean         Clean all build artifacts"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup         # First-time setup (requires sudo)"
    echo "  $0 build-spm     # Build with SPM (may show warnings)"
    echo "  $0 run-xcode     # Build and run with Xcode (recommended)"
    echo "  $0 clean         # Clean all build artifacts"
}

# Main script logic
case "${1:-help}" in
    "setup")
        setup_env
        ;;
    "build-spm")
        build_spm
        ;;
    "build-xcode")
        build_xcode
        ;;
    "run-spm")
        if build_spm; then
            run_app "spm"
        fi
        ;;
    "run-xcode")
        if build_xcode; then
            run_app "xcode"
        fi
        ;;
    "clean")
        clean
        ;;
    "help"|*)
        show_help
        ;;
esac
